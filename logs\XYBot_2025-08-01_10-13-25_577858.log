2025-08-01 10:13:26 | SUCCESS | 读取主设置成功
2025-08-01 10:13:26 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 10:13:26 | INFO | 2025/08/01 10:13:26 GetRedisAddr: 127.0.0.1:6379
2025-08-01 10:13:26 | INFO | 2025/08/01 10:13:26 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 10:13:26 | INFO | 2025/08/01 10:13:26 Server start at :9000
2025-08-01 10:13:27 | SUCCESS | WechatAPI服务已启动
2025-08-01 10:13:27 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 10:13:27 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 10:13:27 | SUCCESS | 登录成功
2025-08-01 10:13:27 | SUCCESS | 已开启自动心跳
2025-08-01 10:13:27 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 10:13:27 | SUCCESS | 数据库初始化成功
2025-08-01 10:13:27 | SUCCESS | 定时任务已启动
2025-08-01 10:13:27 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 10:13:27 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:13:28 | INFO | 播客API初始化成功
2025-08-01 10:13:28 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 10:13:28 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 10:13:28 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 10:13:28 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 10:13:28 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 10:13:28 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 10:13:28 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 10:13:28 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 10:13:28 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 10:13:28 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 10:13:28 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 10:13:28 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 10:13:28 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 10:13:28 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 10:13:28 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 10:13:28 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 10:13:28 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 10:13:28 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 10:13:28 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 10:13:28 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 10:13:28 | DEBUG |   - 启用状态: True
2025-08-01 10:13:28 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 10:13:28 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 10:13:28 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 10:13:28 | DEBUG |   - Cookies配置: 已配置
2025-08-01 10:13:28 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 10:13:28 | DEBUG |   - 自然化响应: True
2025-08-01 10:13:28 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 10:13:29 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-01 10:13:29 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 10:13:29 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 10:13:29 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 10:13:29 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 10:13:29 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 10:13:29 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:13:29 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 10:13:29 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 10:13:29 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 10:13:29 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 10:13:29 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 10:13:29 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 10:13:29 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 10:13:29 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 10:13:29 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 10:13:29 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 10:13:30 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 10:13:30 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 10:13:30 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:13:30 | INFO | [yuanbao] 插件初始化完成
2025-08-01 10:13:30 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 10:13:30 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 10:13:30 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 10:13:30 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 10:13:30 | INFO | 处理堆积消息中
2025-08-01 10:13:30 | SUCCESS | 处理堆积消息完毕
2025-08-01 10:13:30 | SUCCESS | 开始处理消息
2025-08-01 10:13:44 | DEBUG | 收到消息: {'MsgId': 1455234463, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x95sfijdz8xy22:\n进阶免费的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014430, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_esKT4bG8|v1_pazRjV5N</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4992596250753795839, 'MsgSeq': 871416395}
2025-08-01 10:13:44 | INFO | 收到文本消息: 消息ID:1455234463 来自:27852221909@chatroom 发送人:wxid_x95sfijdz8xy22 @:[] 内容:进阶免费的
2025-08-01 10:13:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '进阶免费的' from wxid_x95sfijdz8xy22 in 27852221909@chatroom
2025-08-01 10:13:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['进阶免费的']
2025-08-01 10:13:44 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 10:13:44 | DEBUG | 处理消息内容: '进阶免费的'
2025-08-01 10:13:44 | DEBUG | 消息内容 '进阶免费的' 不匹配任何命令，忽略
2025-08-01 10:14:03 | DEBUG | 收到消息: {'MsgId': 1054034718, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x95sfijdz8xy22:\n下面不是有500周年庆卷可以换一个进阶石'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014448, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_z4Q2+y51|v1_P8KPMHuG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 630256495508498537, 'MsgSeq': 871416396}
2025-08-01 10:14:03 | INFO | 收到文本消息: 消息ID:1054034718 来自:27852221909@chatroom 发送人:wxid_x95sfijdz8xy22 @:[] 内容:下面不是有500周年庆卷可以换一个进阶石
2025-08-01 10:14:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '下面不是有500周年庆卷可以换一个进阶石' from wxid_x95sfijdz8xy22 in 27852221909@chatroom
2025-08-01 10:14:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['下面不是有500周年庆卷可以换一个进阶石']
2025-08-01 10:14:03 | DEBUG | 处理消息内容: '下面不是有500周年庆卷可以换一个进阶石'
2025-08-01 10:14:03 | DEBUG | 消息内容 '下面不是有500周年庆卷可以换一个进阶石' 不匹配任何命令，忽略
2025-08-01 10:14:11 | DEBUG | 收到消息: {'MsgId': 48386503, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x95sfijdz8xy22:\n1000就可以2个了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014457, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_8y6wXjzq|v1_5z/97Fv7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1232913543988073587, 'MsgSeq': 871416397}
2025-08-01 10:14:11 | INFO | 收到文本消息: 消息ID:48386503 来自:27852221909@chatroom 发送人:wxid_x95sfijdz8xy22 @:[] 内容:1000就可以2个了
2025-08-01 10:14:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '1000就可以2个了' from wxid_x95sfijdz8xy22 in 27852221909@chatroom
2025-08-01 10:14:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['1000就可以2个了']
2025-08-01 10:14:11 | DEBUG | 处理消息内容: '1000就可以2个了'
2025-08-01 10:14:11 | DEBUG | 消息内容 '1000就可以2个了' 不匹配任何命令，忽略
2025-08-01 10:14:25 | DEBUG | 收到消息: {'MsgId': 1492787159, 'FromUserName': {'string': '43607022446@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="e4a0eb28ba3b95f58032221387803268" encryver="1" cdnthumbaeskey="e4a0eb28ba3b95f58032221387803268" cdnthumburl="************4b304902010002046b3e4bb802032f5149020416328e710204688c2306042465333662643936322d346231612d343862322d383234632d383464306337396563623631020405250a020201000405004c53d900" cdnthumblength="7789" cdnthumbheight="120" cdnthumbwidth="81" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b304902010002046b3e4bb802032f5149020416328e710204688c2306042465333662643936322d346231612d343862322d383234632d383464306337396563623631020405250a020201000405004c53d900" length="230537" md5="3143e3d787165d08e87ff5e32476bb2f" originsourcemd5="a7bcdaf1e0a3d2d3ad6f2b6e1af7ca90">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014470, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>d05d7175b885dde23f910884a913e8ba_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>76</membercount>\n\t<signature>N0_V1_uR/sD86z|v1_pAtG0htk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '吃瓜小爱\ue124\ue124\u2005在群聊中发了一张图片', 'NewMsgId': 1333434062611139000, 'MsgSeq': 871416398}
2025-08-01 10:14:25 | INFO | 收到图片消息: 消息ID:1492787159 来自:43607022446@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="e4a0eb28ba3b95f58032221387803268" encryver="1" cdnthumbaeskey="e4a0eb28ba3b95f58032221387803268" cdnthumburl="************4b304902010002046b3e4bb802032f5149020416328e710204688c2306042465333662643936322d346231612d343862322d383234632d383464306337396563623631020405250a020201000405004c53d900" cdnthumblength="7789" cdnthumbheight="120" cdnthumbwidth="81" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b304902010002046b3e4bb802032f5149020416328e710204688c2306042465333662643936322d346231612d343862322d383234632d383464306337396563623631020405250a020201000405004c53d900" length="230537" md5="3143e3d787165d08e87ff5e32476bb2f" originsourcemd5="a7bcdaf1e0a3d2d3ad6f2b6e1af7ca90"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 10:14:25 | INFO | [ImageEcho] 保存图片信息成功，当前群 43607022446@chatroom 已存储 5 张图片
2025-08-01 10:14:25 | INFO | [TimerTask] 缓存图片消息: 1492787159
2025-08-01 10:17:12 | DEBUG | 收到消息: {'MsgId': 421870108, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="900a92430c096264e595b35ff0a5caf1" cdnvideourl="************4b304902010002049363814102032f514902049b328e710204688c23ad042438633734396534642d306664342d346538382d393262302d3661626135343335363532380204052408040201000405004c4e6100" cdnthumbaeskey="900a92430c096264e595b35ff0a5caf1" cdnthumburl="************4b304902010002049363814102032f514902049b328e710204688c23ad042438633734396534642d306664342d346538382d393262302d3661626135343335363532380204052408040201000405004c4e6100" length="721853" playlength="8" cdnthumblength="9236" cdnthumbwidth="224" cdnthumbheight="398" fromusername="xiaomaochong" md5="bc765ec9640039c6f95c90cb00bccaed" newmd5="0472ce0a2faf8b2ccf5389bb7397d1df" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014637, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<videopreloadlen>460495</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>e3a4bec50b9b7c4e78058b99825c7ccb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_5XeP6rVM|v1_IzZwNQdm</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 7911513611308770127, 'MsgSeq': 871416399}
2025-08-01 10:17:12 | INFO | 收到视频消息: 消息ID:421870108 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="900a92430c096264e595b35ff0a5caf1" cdnvideourl="************4b304902010002049363814102032f514902049b328e710204688c23ad042438633734396534642d306664342d346538382d393262302d3661626135343335363532380204052408040201000405004c4e6100" cdnthumbaeskey="900a92430c096264e595b35ff0a5caf1" cdnthumburl="************4b304902010002049363814102032f514902049b328e710204688c23ad042438633734396534642d306664342d346538382d393262302d3661626135343335363532380204052408040201000405004c4e6100" length="721853" playlength="8" cdnthumblength="9236" cdnthumbwidth="224" cdnthumbheight="398" fromusername="xiaomaochong" md5="bc765ec9640039c6f95c90cb00bccaed" newmd5="0472ce0a2faf8b2ccf5389bb7397d1df" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-08-01 10:17:35 | DEBUG | 收到消息: {'MsgId': 988046484, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="07c829a94cbc3d6511c19c0cd7428cf5" encryver="1" cdnthumbaeskey="07c829a94cbc3d6511c19c0cd7428cf5" cdnthumburl="************4b30490201000204f705ee8802032f53a102049e8f8cb60204688bb864042438633866386332392d333162382d346365372d623335362d343731303863306237613239020401290a020201000405004c4d3500" cdnthumblength="3607" cdnthumbheight="180" cdnthumbwidth="104" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f705ee8802032f53a102049e8f8cb60204688bb864042438633866386332392d333162382d346365372d623335362d343731303863306237613239020401290a020201000405004c4d3500" length="27023" md5="1c54a1709452ae463fa1cccce3da9d77" originsourcemd5="b4b14e9f35cefbe72fe5d6eb9d7c6669">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014660, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>5a58b66f4ff2a5f3c502c90767c90287_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_sbMWOgow|v1_dpBPK7LN</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 7048911735020285533, 'MsgSeq': 871416400}
2025-08-01 10:17:35 | INFO | 收到图片消息: 消息ID:988046484 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="07c829a94cbc3d6511c19c0cd7428cf5" encryver="1" cdnthumbaeskey="07c829a94cbc3d6511c19c0cd7428cf5" cdnthumburl="************4b30490201000204f705ee8802032f53a102049e8f8cb60204688bb864042438633866386332392d333162382d346365372d623335362d343731303863306237613239020401290a020201000405004c4d3500" cdnthumblength="3607" cdnthumbheight="180" cdnthumbwidth="104" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="************4b30490201000204f705ee8802032f53a102049e8f8cb60204688bb864042438633866386332392d333162382d346365372d623335362d343731303863306237613239020401290a020201000405004c4d3500" length="27023" md5="1c54a1709452ae463fa1cccce3da9d77" originsourcemd5="b4b14e9f35cefbe72fe5d6eb9d7c6669"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 10:17:36 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 10:17:36 | INFO | [TimerTask] 缓存图片消息: 988046484
2025-08-01 10:18:20 | DEBUG | 收到消息: {'MsgId': 423306422, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8w7uvwatvyhi22:\n以前试过把聊天记录导入到4.0，丢了好多图片，都过期了，3.9里也没了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014705, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Sf3Upg9X|v1_DI7ftDWB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'lvye : 以前试过把聊天记录导入到4.0，丢了好多图片，都过期了，3.9里也没...', 'NewMsgId': 3446105320257051170, 'MsgSeq': 871416401}
2025-08-01 10:18:20 | INFO | 收到文本消息: 消息ID:423306422 来自:47325400669@chatroom 发送人:wxid_8w7uvwatvyhi22 @:[] 内容:以前试过把聊天记录导入到4.0，丢了好多图片，都过期了，3.9里也没了
2025-08-01 10:18:20 | DEBUG | [DouBaoImageToImage] 收到文本消息: '以前试过把聊天记录导入到4.0，丢了好多图片，都过期了，3.9里也没了' from wxid_8w7uvwatvyhi22 in 47325400669@chatroom
2025-08-01 10:18:20 | DEBUG | [DouBaoImageToImage] 命令解析: ['以前试过把聊天记录导入到4.0，丢了好多图片，都过期了，3.9里也没了']
2025-08-01 10:18:20 | DEBUG | 处理消息内容: '以前试过把聊天记录导入到4.0，丢了好多图片，都过期了，3.9里也没了'
2025-08-01 10:18:20 | DEBUG | 消息内容 '以前试过把聊天记录导入到4.0，丢了好多图片，都过期了，3.9里也没了' 不匹配任何命令，忽略
2025-08-01 10:19:16 | DEBUG | 收到消息: {'MsgId': 588380449, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n对啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014761, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_rcjxHQ7v|v1_AWo7sFPP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1155079439294511959, 'MsgSeq': 871416402}
2025-08-01 10:19:16 | INFO | 收到文本消息: 消息ID:588380449 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:对啊
2025-08-01 10:19:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '对啊' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-01 10:19:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['对啊']
2025-08-01 10:19:16 | DEBUG | 处理消息内容: '对啊'
2025-08-01 10:19:16 | DEBUG | 消息内容 '对啊' 不匹配任何命令，忽略
2025-08-01 10:19:23 | DEBUG | 收到消息: {'MsgId': 388749436, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n所以我说不是划算吗？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014768, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_lCXjzlxd|v1_wGBDiIEL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 440779032756863459, 'MsgSeq': 871416403}
2025-08-01 10:19:23 | INFO | 收到文本消息: 消息ID:388749436 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:所以我说不是划算吗？
2025-08-01 10:19:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '所以我说不是划算吗？' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-01 10:19:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['所以我说不是划算吗？']
2025-08-01 10:19:23 | DEBUG | 处理消息内容: '所以我说不是划算吗？'
2025-08-01 10:19:23 | DEBUG | 消息内容 '所以我说不是划算吗？' 不匹配任何命令，忽略
2025-08-01 10:19:38 | DEBUG | 收到消息: {'MsgId': 1229619362, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="17000" length="48074" bufid="0" aeskey="485b727af6fcd12971fe6c6ec5b23ff9" voiceurl="3051020100044a304802010002035a663f02032f51490204e83122750204688c243f042432366166333762332d383061302d346133372d393265362d38616462333661653538303902040524000f0201000400" voicemd5="" clientmsgid="41386366366231333863396431366400421019080125d67e8f9fa7d105" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014784, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_OiOpMcsj|v1_IgqdQOTC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 471740673868603266, 'MsgSeq': 871416404}
2025-08-01 10:19:38 | INFO | 收到语音消息: 消息ID:1229619362 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="17000" length="48074" bufid="0" aeskey="485b727af6fcd12971fe6c6ec5b23ff9" voiceurl="3051020100044a304802010002035a663f02032f51490204e83122750204688c243f042432366166333762332d383061302d346133372d393265362d38616462333661653538303902040524000f0201000400" voicemd5="" clientmsgid="41386366366231333863396431366400421019080125d67e8f9fa7d105" fromusername="xiaomaochong" /></msg>
2025-08-01 10:19:41 | DEBUG | 收到消息: {'MsgId': 850074961, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n以前都是500钻石一颗粉色进阶石'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014787, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_VwOAD1Hg|v1_6YbV/8nd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1892908250195293914, 'MsgSeq': 871416405}
2025-08-01 10:19:41 | INFO | 收到文本消息: 消息ID:850074961 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:以前都是500钻石一颗粉色进阶石
2025-08-01 10:19:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '以前都是500钻石一颗粉色进阶石' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-01 10:19:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['以前都是500钻石一颗粉色进阶石']
2025-08-01 10:19:41 | DEBUG | 处理消息内容: '以前都是500钻石一颗粉色进阶石'
2025-08-01 10:19:41 | DEBUG | 消息内容 '以前都是500钻石一颗粉色进阶石' 不匹配任何命令，忽略
2025-08-01 10:20:09 | DEBUG | 收到消息: {'MsgId': 1267918459, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n群里可以转小乔的语音吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014815, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_RTgtbhZB|v1_S/EnyKMk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 群里可以转小乔的语音吗', 'NewMsgId': 6341214108266585358, 'MsgSeq': 871416406}
2025-08-01 10:20:09 | INFO | 收到文本消息: 消息ID:1267918459 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:[] 内容:群里可以转小乔的语音吗
2025-08-01 10:20:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '群里可以转小乔的语音吗' from wxid_laurnst5xn0q22 in 48097389945@chatroom
2025-08-01 10:20:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['群里可以转小乔的语音吗']
2025-08-01 10:20:10 | DEBUG | 处理消息内容: '群里可以转小乔的语音吗'
2025-08-01 10:20:10 | DEBUG | 消息内容 '群里可以转小乔的语音吗' 不匹配任何命令，忽略
2025-08-01 10:20:55 | DEBUG | 收到消息: {'MsgId': 556423239, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'telphy:\n4.0就没人说好的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014860, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_R6tlKbEC|v1_We1nBMt1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '飞天雾 : 4.0就没人说好的', 'NewMsgId': 8175525655672234536, 'MsgSeq': 871416407}
2025-08-01 10:20:55 | INFO | 收到文本消息: 消息ID:556423239 来自:47325400669@chatroom 发送人:telphy @:[] 内容:4.0就没人说好的
2025-08-01 10:20:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: '4.0就没人说好的' from telphy in 47325400669@chatroom
2025-08-01 10:20:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['4.0就没人说好的']
2025-08-01 10:20:55 | DEBUG | 处理消息内容: '4.0就没人说好的'
2025-08-01 10:20:55 | DEBUG | 消息内容 '4.0就没人说好的' 不匹配任何命令，忽略
2025-08-01 10:21:13 | DEBUG | 收到消息: {'MsgId': 601849126, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'telphy:\n界面也丑的一逼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014879, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_x63UZaNw|v1_XhppePQ+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '飞天雾 : 界面也丑的一逼', 'NewMsgId': 6987778747790358352, 'MsgSeq': 871416408}
2025-08-01 10:21:13 | INFO | 收到文本消息: 消息ID:601849126 来自:47325400669@chatroom 发送人:telphy @:[] 内容:界面也丑的一逼
2025-08-01 10:21:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '界面也丑的一逼' from telphy in 47325400669@chatroom
2025-08-01 10:21:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['界面也丑的一逼']
2025-08-01 10:21:13 | DEBUG | 处理消息内容: '界面也丑的一逼'
2025-08-01 10:21:13 | DEBUG | 消息内容 '界面也丑的一逼' 不匹配任何命令，忽略
2025-08-01 10:21:20 | DEBUG | 收到消息: {'MsgId': 414394683, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'Edison-w:\n对'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014885, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Ws74E+0h|v1_ORiUcH4E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\x7f\x7f\x7f\x7f麦来乱 : 对', 'NewMsgId': 869201873351346275, 'MsgSeq': 871416409}
2025-08-01 10:21:20 | INFO | 收到文本消息: 消息ID:414394683 来自:47325400669@chatroom 发送人:Edison-w @:[] 内容:对
2025-08-01 10:21:20 | DEBUG | [DouBaoImageToImage] 收到文本消息: '对' from Edison-w in 47325400669@chatroom
2025-08-01 10:21:20 | DEBUG | [DouBaoImageToImage] 命令解析: ['对']
2025-08-01 10:21:20 | DEBUG | 处理消息内容: '对'
2025-08-01 10:21:20 | DEBUG | 消息内容 '对' 不匹配任何命令，忽略
2025-08-01 10:21:38 | DEBUG | 收到消息: {'MsgId': 858719641, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="39000" length="108244" bufid="0" aeskey="8bc57adffa5b688d7bd99377f4c47ba0" voiceurl="3052020100044b30490201000204dbc0111102032df85f02043a372f700204688c24b7042437353530623239612d396232332d346137302d623333382d66666635663362623434656302040528000f0201000400" voicemd5="" clientmsgid="413361343761656331363738653034004310210801252bab5e6d036106" fromusername="wxid_l9koi6kli78i22" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014903, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_FYoHggk4|v1_mU6QOgfo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一段语音', 'NewMsgId': 2725532178646314735, 'MsgSeq': 871416410}
2025-08-01 10:21:38 | INFO | 收到语音消息: 消息ID:858719641 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="39000" length="108244" bufid="0" aeskey="8bc57adffa5b688d7bd99377f4c47ba0" voiceurl="3052020100044b30490201000204dbc0111102032df85f02043a372f700204688c24b7042437353530623239612d396232332d346137302d623333382d66666635663362623434656302040528000f0201000400" voicemd5="" clientmsgid="413361343761656331363738653034004310210801252bab5e6d036106" fromusername="wxid_l9koi6kli78i22" /></msg>
2025-08-01 10:21:40 | DEBUG | 收到消息: {'MsgId': 556993411, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'Edison-w:\n我也不喜欢4.0'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014905, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_InTg63Ow|v1_lhU+unF2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\x7f\x7f\x7f\x7f麦来乱 : 我也不喜欢4.0', 'NewMsgId': 375777077073792958, 'MsgSeq': 871416411}
2025-08-01 10:21:40 | INFO | 收到文本消息: 消息ID:556993411 来自:47325400669@chatroom 发送人:Edison-w @:[] 内容:我也不喜欢4.0
2025-08-01 10:21:40 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我也不喜欢4.0' from Edison-w in 47325400669@chatroom
2025-08-01 10:21:40 | DEBUG | [DouBaoImageToImage] 命令解析: ['我也不喜欢4.0']
2025-08-01 10:21:40 | DEBUG | 处理消息内容: '我也不喜欢4.0'
2025-08-01 10:21:40 | DEBUG | 消息内容 '我也不喜欢4.0' 不匹配任何命令，忽略
2025-08-01 10:21:56 | DEBUG | 收到消息: {'MsgId': 1355796082, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="d884353358b361d736650d0c545aac07" len="50865" productid="" androidmd5="d884353358b361d736650d0c545aac07" androidlen="50865" s60v3md5="d884353358b361d736650d0c545aac07" s60v3len="50865" s60v5md5="d884353358b361d736650d0c545aac07" s60v5len="50865" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=d884353358b361d736650d0c545aac07&amp;filekey=30440201010430302e02016e0402534804206438383433353333353862333631643733363635306430633534356161633037020300c6b1040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000bfe256c8a355e0000006e01004fb153481843603156573f36c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=f43b05c3968c31b7d82993757e59a91d&amp;filekey=30440201010430302e02016e0402534804206634336230356333393638633331623764383239393337353765353961393164020300c6c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000c83936c8a355e0000006e02004fb253481843603156573f382&amp;ef=2&amp;bizid=1022" aeskey="119a7327626d4b24b7c42d52abb96865" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=5b407b405193abd441e88f6d3b0b34dd&amp;filekey=3043020101042f302d02016e040253480420356234303762343035313933616264343431653838663664336230623334646402021080040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000e1e096c8a355e0000006e03004fb353481843603156573f3a8&amp;ef=3&amp;bizid=1022" externmd5="ac72583a1d0162cd2943243c3e15f83c" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014922, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_yu7u0MZp|v1_gzsvkA41</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 6802800539816351961, 'MsgSeq': 871416412}
2025-08-01 10:21:56 | INFO | 收到表情消息: 消息ID:1355796082 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:d884353358b361d736650d0c545aac07 大小:50865
