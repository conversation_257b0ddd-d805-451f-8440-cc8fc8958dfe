2025-08-01 09:22:50 | SUCCESS | 读取主设置成功
2025-08-01 09:22:50 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 09:22:50 | INFO | 2025/08/01 09:22:50 GetRedisAddr: 127.0.0.1:6379
2025-08-01 09:22:50 | INFO | 2025/08/01 09:22:50 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 09:22:50 | INFO | 2025/08/01 09:22:50 Server start at :9000
2025-08-01 09:22:50 | SUCCESS | WechatAPI服务已启动
2025-08-01 09:22:51 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 09:22:51 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 09:22:51 | SUCCESS | 登录成功
2025-08-01 09:22:51 | SUCCESS | 已开启自动心跳
2025-08-01 09:22:51 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:22:51 | SUCCESS | 数据库初始化成功
2025-08-01 09:22:51 | SUCCESS | 定时任务已启动
2025-08-01 09:22:51 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 09:22:51 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:22:52 | INFO | 播客API初始化成功
2025-08-01 09:22:52 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:22:52 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 09:22:52 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 09:22:52 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 09:22:52 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 09:22:52 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 09:22:52 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 09:22:52 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 09:22:52 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 09:22:52 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 09:22:52 | DEBUG |   - 启用状态: True
2025-08-01 09:22:52 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 09:22:52 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 09:22:52 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 09:22:52 | DEBUG |   - Cookies配置: 已配置
2025-08-01 09:22:52 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 09:22:52 | DEBUG |   - 自然化响应: True
2025-08-01 09:22:52 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 09:22:52 | ERROR | 加载插件时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 51, in load_plugin
    plugin = plugin_class()
             ^^^^^^^^^^^^^^
  File "C:\XYBotV2\plugins\MiniProgramTester\main.py", line 24, in __init__
    self._init_natural_responses()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MiniProgramTester' object has no attribute '_init_natural_responses'

2025-08-01 09:22:52 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-01 09:22:52 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 09:22:52 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 09:22:52 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 09:22:52 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 09:22:52 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 09:22:52 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:22:52 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 09:22:52 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 09:22:52 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 09:22:52 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 09:22:52 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 09:22:52 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 09:22:52 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 09:22:53 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 09:22:53 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 09:22:53 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 09:22:53 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 09:22:54 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 09:22:54 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:22:54 | INFO | [yuanbao] 插件初始化完成
2025-08-01 09:22:54 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 09:22:54 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 09:22:54 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 09:22:54 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 09:22:54 | INFO | 处理堆积消息中
2025-08-01 09:22:54 | SUCCESS | 处理堆积消息完毕
2025-08-01 09:22:54 | SUCCESS | 开始处理消息
2025-08-01 09:22:55 | DEBUG | 收到消息: {'MsgId': 1800315910, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="c7e27e1d454f532eddb1060c13db05cf" encryver="1" cdnthumbaeskey="c7e27e1d454f532eddb1060c13db05cf" cdnthumburl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" cdnthumblength="4880" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" length="95033" md5="8274777b21108b2c6fe61380899dfbfe" originsourcemd5="5b7fd7c2a948698bc376d31de554bff7">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011380, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>36d60c1e2464b50ae17d0bc01ebfa4d7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_ZLPzhQR8|v1_zC0G+nrO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 1175481510760757500, 'MsgSeq': 871416284}
2025-08-01 09:22:55 | INFO | 收到图片消息: 消息ID:1800315910 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="c7e27e1d454f532eddb1060c13db05cf" encryver="1" cdnthumbaeskey="c7e27e1d454f532eddb1060c13db05cf" cdnthumburl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" cdnthumblength="4880" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204203122750204688c16f4042430656536393765652d366465642d343033302d383932382d303161346661636430326230020405250a020201000405004c4dfd00" length="95033" md5="8274777b21108b2c6fe61380899dfbfe" originsourcemd5="5b7fd7c2a948698bc376d31de554bff7"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:22:56 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 09:22:56 | INFO | [TimerTask] 缓存图片消息: 1800315910
2025-08-01 09:23:05 | DEBUG | 收到消息: {'MsgId': 1496175978, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n@\xa0执傲\u2005给你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011390, 'MsgSource': '<msgsource>\n\t<atuserlist>qq631390473</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_F2wFMXYj|v1_8vtuIIhf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2889355027434464434, 'MsgSeq': 871416285}
2025-08-01 09:23:05 | INFO | 收到文本消息: 消息ID:1496175978 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:['qq631390473'] 内容:@ 执傲 给你
2025-08-01 09:23:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@ 执傲 给你' from wxid_5kipwrzramxr22 in 27852221909@chatroom
2025-08-01 09:23:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['@\xa0执傲\u2005给你']
2025-08-01 09:23:05 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:23:05 | DEBUG | 处理消息内容: '@ 执傲 给你'
2025-08-01 09:23:05 | DEBUG | 消息内容 '@ 执傲 给你' 不匹配任何命令，忽略
2025-08-01 09:23:10 | DEBUG | 收到消息: {'MsgId': 2047340988, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7c9d966e393ad4687c584073acbc3a47" encryver="1" cdnthumbaeskey="7c9d966e393ad4687c584073acbc3a47" cdnthumburl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" cdnthumblength="2742" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" length="61305" cdnbigimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" hdlength="2789224" md5="0bf26f1ea12444ef4415d26b527f61e2" hevc_mid_size="61305" originsourcemd5="0bf26f1ea12444ef4415d26b527f61e2">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjU1MDA1MDAwNTE0MDQxNTAiLCJwZHFoYXNoIjoiMzY3NTJhZTkzZDczZTk2MmQzZDJlNzczODk4ODQ0NzEyOGM5MTljODY3NjNhMjllZDY3N2UzYzIyMjYzNWUyNSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011395, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>e9cbc212e94d1e8d6ef2c2f9526388bb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_6dGd4kY4|v1_rLKBHYfY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2468378362155633300, 'MsgSeq': 871416286}
2025-08-01 09:23:10 | INFO | 收到图片消息: 消息ID:2047340988 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 XML:<?xml version="1.0"?><msg><img aeskey="7c9d966e393ad4687c584073acbc3a47" encryver="1" cdnthumbaeskey="7c9d966e393ad4687c584073acbc3a47" cdnthumburl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" cdnthumblength="2742" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" length="61305" cdnbigimgurl="3057020100044b304902010002042af40fe002032dcdc9020453e8d3740204688c1702042439626438636665332d316263652d343732352d626632362d306365646438666638646162020405252a010201000405004c4e6100" hdlength="2789224" md5="0bf26f1ea12444ef4415d26b527f61e2" hevc_mid_size="61305" originsourcemd5="0bf26f1ea12444ef4415d26b527f61e2"><secHashInfoBase64>eyJwaGFzaCI6IjU1MDA1MDAwNTE0MDQxNTAiLCJwZHFoYXNoIjoiMzY3NTJhZTkzZDczZTk2MmQzZDJlNzczODk4ODQ0NzEyOGM5MTljODY3NjNhMjllZDY3N2UzYzIyMjYzNWUyNSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:23:10 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:23:10 | INFO | [TimerTask] 缓存图片消息: 2047340988
2025-08-01 09:23:14 | DEBUG | 收到消息: {'MsgId': 430328861, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n基金我就挣了一天钱，现在天天亏'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011399, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_qeCFNsiZ|v1_KvJxH59l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 基金我就挣了一天钱，现在天天亏', 'NewMsgId': 8497724168757424674, 'MsgSeq': 871416287}
2025-08-01 09:23:14 | INFO | 收到文本消息: 消息ID:430328861 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:基金我就挣了一天钱，现在天天亏
2025-08-01 09:23:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '基金我就挣了一天钱，现在天天亏' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:23:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['基金我就挣了一天钱，现在天天亏']
2025-08-01 09:23:15 | DEBUG | 处理消息内容: '基金我就挣了一天钱，现在天天亏'
2025-08-01 09:23:15 | DEBUG | 消息内容 '基金我就挣了一天钱，现在天天亏' 不匹配任何命令，忽略
2025-08-01 09:23:17 | DEBUG | 收到消息: {'MsgId': 646610057, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011402, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_IS4H13gx|v1_XRSyH+XE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : [捂脸]', 'NewMsgId': 2053202181805452766, 'MsgSeq': 871416288}
2025-08-01 09:23:17 | INFO | 收到表情消息: 消息ID:646610057 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:[捂脸]
2025-08-01 09:23:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2053202181805452766
2025-08-01 09:23:33 | DEBUG | 收到消息: {'MsgId': 1985628658, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@小爱\u2005你买不少基金啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011418, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[,xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_xLF2UfCZ|v1_820ri1xQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @小爱\u2005你买不少基金啊', 'NewMsgId': 2427124814579801363, 'MsgSeq': 871416289}
2025-08-01 09:23:33 | INFO | 收到文本消息: 消息ID:1985628658 来自:48097389945@chatroom 发送人:zll953369865 @:['xiaomaochong'] 内容:@小爱 你买不少基金啊
2025-08-01 09:23:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@小爱 你买不少基金啊' from zll953369865 in 48097389945@chatroom
2025-08-01 09:23:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['@小爱\u2005你买不少基金啊']
2025-08-01 09:23:33 | DEBUG | 处理消息内容: '@小爱 你买不少基金啊'
2025-08-01 09:23:33 | DEBUG | 消息内容 '@小爱 你买不少基金啊' 不匹配任何命令，忽略
2025-08-01 09:23:42 | DEBUG | 收到消息: {'MsgId': 1319560354, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n都买到5000多啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011427, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_qJWhQjuG|v1_BlL1Yeql</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 都买到5000多啊', 'NewMsgId': 9019350774400702491, 'MsgSeq': 871416290}
2025-08-01 09:23:42 | INFO | 收到文本消息: 消息ID:1319560354 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:都买到5000多啊
2025-08-01 09:23:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '都买到5000多啊' from zll953369865 in 48097389945@chatroom
2025-08-01 09:23:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['都买到5000多啊']
2025-08-01 09:23:42 | DEBUG | 处理消息内容: '都买到5000多啊'
2025-08-01 09:23:42 | DEBUG | 消息内容 '都买到5000多啊' 不匹配任何命令，忽略
2025-08-01 09:24:36 | DEBUG | 收到消息: {'MsgId': 644778143, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n就跟着你你们买的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011481, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_PCGjHafy|v1_uA6EZkKR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 就跟着你你们买的', 'NewMsgId': 1340312585900742986, 'MsgSeq': 871416291}
2025-08-01 09:24:36 | INFO | 收到文本消息: 消息ID:644778143 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:就跟着你你们买的
2025-08-01 09:24:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '就跟着你你们买的' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:24:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['就跟着你你们买的']
2025-08-01 09:24:36 | DEBUG | 处理消息内容: '就跟着你你们买的'
2025-08-01 09:24:36 | DEBUG | 消息内容 '就跟着你你们买的' 不匹配任何命令，忽略
2025-08-01 09:24:48 | DEBUG | 收到消息: {'MsgId': 756614876, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n@帅\u2005加油签'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011494, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ubbh6q832tcs21]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_nvm3e/Ao|v1_NiOLRw1G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6449448009461128319, 'MsgSeq': 871416292}
2025-08-01 09:24:48 | INFO | 收到文本消息: 消息ID:756614876 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:['wxid_ubbh6q832tcs21'] 内容:@帅 加油签
2025-08-01 09:24:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@帅 加油签' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-01 09:24:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['@帅\u2005加油签']
2025-08-01 09:24:48 | DEBUG | 处理消息内容: '@帅 加油签'
2025-08-01 09:24:48 | DEBUG | 消息内容 '@帅 加油签' 不匹配任何命令，忽略
2025-08-01 09:24:50 | DEBUG | 收到消息: {'MsgId': 1862792712, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n结果你们都把钱挣结束了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011496, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_GPKTufal|v1_NVhhSAvh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 结果你们都把钱挣结束了', 'NewMsgId': 1282589282716655359, 'MsgSeq': 871416293}
2025-08-01 09:24:50 | INFO | 收到文本消息: 消息ID:1862792712 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:结果你们都把钱挣结束了
2025-08-01 09:24:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '结果你们都把钱挣结束了' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:24:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['结果你们都把钱挣结束了']
2025-08-01 09:24:51 | DEBUG | 处理消息内容: '结果你们都把钱挣结束了'
2025-08-01 09:24:51 | DEBUG | 消息内容 '结果你们都把钱挣结束了' 不匹配任何命令，忽略
2025-08-01 09:25:09 | DEBUG | 收到消息: {'MsgId': 170622444, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n我去刚好接盘[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011514, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_+bc57qio|v1_MjpqkvFT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 我去刚好接盘[抠鼻]', 'NewMsgId': 3939714625764744656, 'MsgSeq': 871416294}
2025-08-01 09:25:09 | INFO | 收到文本消息: 消息ID:170622444 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:我去刚好接盘[抠鼻]
2025-08-01 09:25:09 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我去刚好接盘[抠鼻]' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:25:09 | DEBUG | [DouBaoImageToImage] 命令解析: ['我去刚好接盘[抠鼻]']
2025-08-01 09:25:09 | DEBUG | 处理消息内容: '我去刚好接盘[抠鼻]'
2025-08-01 09:25:09 | DEBUG | 消息内容 '我去刚好接盘[抠鼻]' 不匹配任何命令，忽略
2025-08-01 09:25:23 | DEBUG | 收到消息: {'MsgId': 1144663243, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="814f8a419dc4e4cfd8c091bd3d339109" len = "332819" productid="" androidmd5="814f8a419dc4e4cfd8c091bd3d339109" androidlen="332819" s60v3md5 = "814f8a419dc4e4cfd8c091bd3d339109" s60v3len="332819" s60v5md5 = "814f8a419dc4e4cfd8c091bd3d339109" s60v5len="332819" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=814f8a419dc4e4cfd8c091bd3d339109&amp;filekey=30440201010430302e02016e04025348042038313466386134313964633465346366643863303931626433643333393130390203051413040d00000004627466730000000132&amp;hy=SH&amp;storeid=267fcf2250005c110f0721bff0000006e01004fb153482d57f0d156c1e8f79&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=6be0ffeef8c0f192f7643a7f02ed20fb&amp;filekey=30440201010430302e02016e04025348042036626530666665656638633066313932663736343361376630326564323066620203051420040d00000004627466730000000132&amp;hy=SH&amp;storeid=267fcf2250006f295f0721bff0000006e02004fb253482d57f0d156c1e8f9f&amp;ef=2&amp;bizid=1022" aeskey= "2bcef57c59a247aea00b9c1ad0b7dced" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=7526afeec26f4459adff5e2a6e408fb8&amp;filekey=30440201010430302e02016e0402534804203735323661666565633236663434353961646666356532613665343038666238020300d690040d00000004627466730000000132&amp;hy=SH&amp;storeid=267fcf22500081eb0f0721bff0000006e03004fb353482d57f0d156c1e8faf&amp;ef=3&amp;bizid=1022" externmd5 = "df2964ceeb65b126ae6431562a57da39" width= "400" height= "400" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011529, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_Jh+hc114|v1_Z6FvbZPn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 4382026239957435068, 'MsgSeq': 871416295}
2025-08-01 09:25:23 | INFO | 收到表情消息: 消息ID:1144663243 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:814f8a419dc4e4cfd8c091bd3d339109 大小:332819
2025-08-01 09:25:24 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4382026239957435068
2025-08-01 09:25:28 | DEBUG | 收到消息: {'MsgId': 1900278685, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="e2fd7a5a0cfa7e15376dd9601f39382c" cdnvideourl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumbaeskey="e2fd7a5a0cfa7e15376dd9601f39382c" cdnthumburl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" length="3701070" playlength="85" cdnthumblength="1527" cdnthumbwidth="288" cdnthumbheight="159" fromusername="zuoledd" md5="08c5d166d5b95d27647af43c55d41458" newmd5="a81da1ee6f2ed833989e6f7ccf23605d" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011533, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<weappsourceUsername>(null),zuoledd</weappsourceUsername>\n\t<sec_msg_node>\n\t\t<uuid>288a4229371fb0164626657fdee962ec_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_xIhz5vYV|v1_lhL6GwEB</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一段视频', 'NewMsgId': 1160582169123714834, 'MsgSeq': 871416296}
2025-08-01 09:25:28 | INFO | 收到视频消息: 消息ID:1900278685 来自:48097389945@chatroom 发送人:zuoledd XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="e2fd7a5a0cfa7e15376dd9601f39382c" cdnvideourl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumbaeskey="e2fd7a5a0cfa7e15376dd9601f39382c" cdnthumburl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" length="3701070" playlength="85" cdnthumblength="1527" cdnthumbwidth="288" cdnthumbheight="159" fromusername="zuoledd" md5="08c5d166d5b95d27647af43c55d41458" newmd5="a81da1ee6f2ed833989e6f7ccf23605d" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-08-01 09:25:32 | DEBUG | 收到消息: {'MsgId': 1099570933, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="8ffb639435107dd99c194ea20ed3bb62" len = "189062" productid="" androidmd5="8ffb639435107dd99c194ea20ed3bb62" androidlen="189062" s60v3md5 = "8ffb639435107dd99c194ea20ed3bb62" s60v3len="189062" s60v5md5 = "8ffb639435107dd99c194ea20ed3bb62" s60v5len="189062" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=8ffb639435107dd99c194ea20ed3bb62&amp;filekey=30440201010430302e02016e0402535a04203866666236333934333531303764643939633139346561323065643362623632020302e286040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268368a050002b626c95cd9040000006e01004fb1535a2f16dbc1e0a8083f2&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=58ccf287095457f52a0dc72690336a11&amp;filekey=30440201010430302e02016e0402535a04203538636366323837303935343537663532613064633732363930333336613131020302e290040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268368a0500043902c95cd9040000006e02004fb2535a2f16dbc1e0a808413&amp;ef=2&amp;bizid=1022" aeskey= "c869b44b5e4643a9a5230a8c3b76b9ed" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=e7086c5cbf58afc9e165a0d067d3e132&amp;filekey=3043020101042f302d02016e0402535a0420653730383663356362663538616663396531363561306430363764336531333202026600040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268368a0500053459c95cd9040000006e03004fb3535a2f16dbc1e0a80842e&amp;ef=3&amp;bizid=1022" externmd5 = "68214225d5533d429b10e091ef09f291" width= "400" height= "400" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011537, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_JgEbsWO7|v1_2A1VAPJT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 9111916603517847444, 'MsgSeq': 871416297}
2025-08-01 09:25:32 | INFO | 收到表情消息: 消息ID:1099570933 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:8ffb639435107dd99c194ea20ed3bb62 大小:189062
2025-08-01 09:25:32 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9111916603517847444
2025-08-01 09:25:45 | DEBUG | 收到消息: {'MsgId': 685857577, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n幸亏买的少[呲牙]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011550, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_D98wuVja|v1_PZvJ657K</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 幸亏买的少[呲牙]', 'NewMsgId': 3007205477286087527, 'MsgSeq': 871416298}
2025-08-01 09:25:45 | INFO | 收到文本消息: 消息ID:685857577 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:幸亏买的少[呲牙]
2025-08-01 09:25:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '幸亏买的少[呲牙]' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:25:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['幸亏买的少[呲牙]']
2025-08-01 09:25:46 | DEBUG | 处理消息内容: '幸亏买的少[呲牙]'
2025-08-01 09:25:46 | DEBUG | 消息内容 '幸亏买的少[呲牙]' 不匹配任何命令，忽略
2025-08-01 09:26:07 | DEBUG | 收到消息: {'MsgId': 35610036, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n买基金 本来就是有赚有赔啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011572, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_Sh/uNmQu|v1_cOYVPP5k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 买基金 本来就是有赚有赔啊', 'NewMsgId': 1082012815008624727, 'MsgSeq': 871416299}
2025-08-01 09:26:07 | INFO | 收到文本消息: 消息ID:35610036 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:买基金 本来就是有赚有赔啊
2025-08-01 09:26:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '买基金 本来就是有赚有赔啊' from zll953369865 in 48097389945@chatroom
2025-08-01 09:26:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['买基金', '本来就是有赚有赔啊']
2025-08-01 09:26:07 | DEBUG | 处理消息内容: '买基金 本来就是有赚有赔啊'
2025-08-01 09:26:07 | DEBUG | 消息内容 '买基金 本来就是有赚有赔啊' 不匹配任何命令，忽略
2025-08-01 09:26:16 | DEBUG | 收到消息: {'MsgId': 1715504198, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n挣钱了 你就不说了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011581, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_U+MTkp1K|v1_P86mmglc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 挣钱了 你就不说了', 'NewMsgId': 4209548076875803665, 'MsgSeq': 871416300}
2025-08-01 09:26:16 | INFO | 收到文本消息: 消息ID:1715504198 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:挣钱了 你就不说了
2025-08-01 09:26:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '挣钱了 你就不说了' from zll953369865 in 48097389945@chatroom
2025-08-01 09:26:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['挣钱了', '你就不说了']
2025-08-01 09:26:16 | DEBUG | 处理消息内容: '挣钱了 你就不说了'
2025-08-01 09:26:16 | DEBUG | 消息内容 '挣钱了 你就不说了' 不匹配任何命令，忽略
2025-08-01 09:26:18 | DEBUG | 收到消息: {'MsgId': 2077414146, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="a7a17721184c342a0209eef76a373dc4" cdnvideourl="3057020100044b304902010002040e4bfc7b02032f5b7102041b7810da0204688b7386042433656434633932632d643362362d343365612d383064392d6166353438373837366666340204052808040201000405004c505500" cdnthumbaeskey="a7a17721184c342a0209eef76a373dc4" cdnthumburl="3057020100044b304902010002040e4bfc7b02032f5b7102041b7810da0204688b7386042433656434633932632d643362362d343365612d383064392d6166353438373837366666340204052808040201000405004c505500" length="9420729" playlength="60" cdnthumblength="28203" cdnthumbwidth="306" cdnthumbheight="540" fromusername="zuoledd" md5="37ef80605434ffb8e8a46a945d80cb6f" newmd5="228daad72f46451e54f593d5268b131c" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011582, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<weappsourceUsername>(null),wxid_1q60hw5n1kkg12,zuoledd</weappsourceUsername>\n\t<sec_msg_node>\n\t\t<uuid>7eb233c8325eef642aea00b794cfdc83_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_tatAlwHd|v1_5TpRbEvJ</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一段视频', 'NewMsgId': 1031706493863117479, 'MsgSeq': 871416301}
2025-08-01 09:26:18 | INFO | 收到视频消息: 消息ID:2077414146 来自:48097389945@chatroom 发送人:zuoledd XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="a7a17721184c342a0209eef76a373dc4" cdnvideourl="3057020100044b304902010002040e4bfc7b02032f5b7102041b7810da0204688b7386042433656434633932632d643362362d343365612d383064392d6166353438373837366666340204052808040201000405004c505500" cdnthumbaeskey="a7a17721184c342a0209eef76a373dc4" cdnthumburl="3057020100044b304902010002040e4bfc7b02032f5b7102041b7810da0204688b7386042433656434633932632d643362362d343365612d383064392d6166353438373837366666340204052808040201000405004c505500" length="9420729" playlength="60" cdnthumblength="28203" cdnthumbwidth="306" cdnthumbheight="540" fromusername="zuoledd" md5="37ef80605434ffb8e8a46a945d80cb6f" newmd5="228daad72f46451e54f593d5268b131c" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-08-01 09:26:59 | DEBUG | 收到消息: {'MsgId': 272909751, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d906b505d0bbb711ff108ec84d3a6e8d" encryver="1" cdnthumbaeskey="d906b505d0bbb711ff108ec84d3a6e8d" cdnthumburl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" cdnthumblength="3148" cdnthumbheight="120" cdnthumbwidth="95" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" length="9144" md5="ce14b78d0e961d488da97a57b3e70d9f" originsourcemd5="9112063822eedd35c66ad3380f87dcff">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011624, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>b76fd857471cd9f115c11482bfadeb45_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_FUvJ5leQ|v1_UiJ61mW4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 6352337131112617991, 'MsgSeq': 871416302}
2025-08-01 09:26:59 | INFO | 收到图片消息: 消息ID:272909751 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="d906b505d0bbb711ff108ec84d3a6e8d" encryver="1" cdnthumbaeskey="d906b505d0bbb711ff108ec84d3a6e8d" cdnthumburl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" cdnthumblength="3148" cdnthumbheight="120" cdnthumbwidth="95" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" length="9144" md5="ce14b78d0e961d488da97a57b3e70d9f" originsourcemd5="9112063822eedd35c66ad3380f87dcff"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:26:59 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 09:26:59 | INFO | [TimerTask] 缓存图片消息: 272909751
2025-08-01 09:27:01 | DEBUG | 收到消息: {'MsgId': 629692246, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'lyj123456zl:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>手推式</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>43</type>\n\t\t\t<svrid>1160582169123714834</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zuoledd</chatusr>\n\t\t\t<displayname>作乐多端</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;videomsg length="3701070" playlength="85" offset="0" rawoffset="0" fromusername="zuoledd" status="6" cameratype="0" source="1"                                              aeskey="e2fd7a5a0cfa7e15376dd9601f39382c" cdnvideourl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumburl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumblength="1527" cdnthumbwidth="288" cdnthumbheight="159" cdnthumbaeskey="e2fd7a5a0cfa7e15376dd9601f39382c" encryver="1" fileparam="" md5 ="08c5d166d5b95d27647af43c55d41458" newmd5 ="a81da1ee6f2ed833989e6f7ccf23605d" originsourcemd5 =""  filekey="48097389945@chatroom_88133_1754011533" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  videoFormat="2" rawVideoFormat="0" &gt;&lt;/videomsg&gt;&lt;statextstr&gt;&lt;/statextstr&gt;&lt;encodejson&gt;&lt;![CDATA[]]&gt;&lt;/encodejson&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;876934025&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;5&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;weappsourceUsername&gt;(null),zuoledd&lt;/weappsourceUsername&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;288a4229371fb0164626657fdee962ec_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;videomsg_pd cdnvideourl_size="3701070" cdnvideourl_score_all="1:10000;" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_1OFK2/5h|v1_bM8hy+Gd&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754011533</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>lyj123456zl</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011626, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3331ed0083d28d4b6d8e7abc26fe3258_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_Dka/pfjy|v1_6M4RnQVV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿杰 : 手推式', 'NewMsgId': 54058397331585684, 'MsgSeq': 871416303}
2025-08-01 09:27:01 | DEBUG | 从群聊消息中提取发送者: lyj123456zl
2025-08-01 09:27:01 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:27:01 | INFO | 收到引用消息: 消息ID:629692246 来自:48097389945@chatroom 发送人:lyj123456zl 内容:手推式 引用类型:43
2025-08-01 09:27:02 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:27:02 | INFO | [DouBaoImageToImage] 消息内容: '手推式' from lyj123456zl in 48097389945@chatroom
2025-08-01 09:27:02 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['手推式']
2025-08-01 09:27:02 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 09:27:02 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:27:02 | INFO |   - 消息内容: 手推式
2025-08-01 09:27:02 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 09:27:02 | INFO |   - 发送人: lyj123456zl
2025-08-01 09:27:02 | INFO |   - 引用信息: {'MsgType': 43, 'Content': '<msg><videomsg length="3701070" playlength="85" offset="0" rawoffset="0" fromusername="zuoledd" status="6" cameratype="0" source="1"                                              aeskey="e2fd7a5a0cfa7e15376dd9601f39382c" cdnvideourl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumburl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumblength="1527" cdnthumbwidth="288" cdnthumbheight="159" cdnthumbaeskey="e2fd7a5a0cfa7e15376dd9601f39382c" encryver="1" fileparam="" md5 ="08c5d166d5b95d27647af43c55d41458" newmd5 ="a81da1ee6f2ed833989e6f7ccf23605d" originsourcemd5 =""  filekey="48097389945@chatroom_88133_1754011533" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  videoFormat="2" rawVideoFormat="0" ></videomsg><statextstr></statextstr><encodejson><![CDATA[]]></encodejson></msg>', 'Msgid': '1160582169123714834', 'NewMsgId': '1160582169123714834', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '作乐多端', 'MsgSource': '<msgsource><sequence_id>876934025</sequence_id>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<weappsourceUsername>(null),zuoledd</weappsourceUsername>\n\t<sec_msg_node>\n\t\t<uuid>288a4229371fb0164626657fdee962ec_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<videomsg_pd cdnvideourl_size="3701070" cdnvideourl_score_all="1:10000;" />\n\t<silence>1</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_1OFK2/5h|v1_bM8hy+Gd</signature>\n</msgsource>\n', 'Createtime': '1754011533', 'SenderWxid': 'lyj123456zl'}
2025-08-01 09:27:02 | INFO |   - 引用消息ID: 
2025-08-01 09:27:02 | INFO |   - 引用消息类型: 
2025-08-01 09:27:02 | INFO |   - 引用消息内容: <msg><videomsg length="3701070" playlength="85" offset="0" rawoffset="0" fromusername="zuoledd" status="6" cameratype="0" source="1"                                              aeskey="e2fd7a5a0cfa7e15376dd9601f39382c" cdnvideourl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumburl="3057020100044b3049020100020438ae94a602032f54cf020465c6ccb70204688c0f2e042437383133663439332d303433392d343831652d626662312d6637396462313832303064660204092808040201000405004c537700" cdnthumblength="1527" cdnthumbwidth="288" cdnthumbheight="159" cdnthumbaeskey="e2fd7a5a0cfa7e15376dd9601f39382c" encryver="1" fileparam="" md5 ="08c5d166d5b95d27647af43c55d41458" newmd5 ="a81da1ee6f2ed833989e6f7ccf23605d" originsourcemd5 =""  filekey="48097389945@chatroom_88133_1754011533" uploadcontinuecount="0" rawlength="0" rawmd5="" cdnrawvideourl="" cdnrawvideoaeskey="" overwritemsgcreatetime="0" overwritenewmsgid="0" videouploadtoken="" isplaceholder="0" rawuploadcontinuecount="0"  videoFormat="2" rawVideoFormat="0" ></videomsg><statextstr></statextstr><encodejson><![CDATA[]]></encodejson></msg>
2025-08-01 09:27:02 | INFO |   - 引用消息发送人: lyj123456zl
2025-08-01 09:27:08 | DEBUG | 收到消息: {'MsgId': 1014811207, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n我的稳利宝给我补上了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011633, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_MphSQkYn|v1_iQHRIlzR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 我的稳利宝给我补上了', 'NewMsgId': 3438787073751368673, 'MsgSeq': 871416304}
2025-08-01 09:27:08 | INFO | 收到文本消息: 消息ID:1014811207 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:我的稳利宝给我补上了
2025-08-01 09:27:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我的稳利宝给我补上了' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:27:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['我的稳利宝给我补上了']
2025-08-01 09:27:08 | DEBUG | 处理消息内容: '我的稳利宝给我补上了'
2025-08-01 09:27:08 | DEBUG | 消息内容 '我的稳利宝给我补上了' 不匹配任何命令，忽略
2025-08-01 09:27:14 | DEBUG | 收到消息: {'MsgId': 1953478514, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n牛逼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011640, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_mMLlRGsr|v1_XjsVGzsW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 牛逼', 'NewMsgId': 5577004654080238100, 'MsgSeq': 871416305}
2025-08-01 09:27:14 | INFO | 收到文本消息: 消息ID:1953478514 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:牛逼
2025-08-01 09:27:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '牛逼' from zll953369865 in 48097389945@chatroom
2025-08-01 09:27:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['牛逼']
2025-08-01 09:27:15 | DEBUG | 处理消息内容: '牛逼'
2025-08-01 09:27:15 | DEBUG | 消息内容 '牛逼' 不匹配任何命令，忽略
2025-08-01 09:27:23 | DEBUG | 收到消息: {'MsgId': 1577031164, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n挣钱了也说啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011648, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_w8ODfRe9|v1_cP7g7b+k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 挣钱了也说啊', 'NewMsgId': 6931268490720068213, 'MsgSeq': 871416306}
2025-08-01 09:27:23 | INFO | 收到文本消息: 消息ID:1577031164 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:挣钱了也说啊
2025-08-01 09:27:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '挣钱了也说啊' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:27:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['挣钱了也说啊']
2025-08-01 09:27:23 | DEBUG | 处理消息内容: '挣钱了也说啊'
2025-08-01 09:27:23 | DEBUG | 消息内容 '挣钱了也说啊' 不匹配任何命令，忽略
2025-08-01 09:27:32 | DEBUG | 收到消息: {'MsgId': 156665760, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n我每天都在说[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011657, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_aE9fLzQS|v1_WYG/4mYA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 我每天都在说[抠鼻]', 'NewMsgId': 2201626593308227176, 'MsgSeq': 871416307}
2025-08-01 09:27:32 | INFO | 收到文本消息: 消息ID:156665760 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:我每天都在说[抠鼻]
2025-08-01 09:27:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我每天都在说[抠鼻]' from xiaomaochong in 48097389945@chatroom
2025-08-01 09:27:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['我每天都在说[抠鼻]']
2025-08-01 09:27:32 | DEBUG | 处理消息内容: '我每天都在说[抠鼻]'
2025-08-01 09:27:32 | DEBUG | 消息内容 '我每天都在说[抠鼻]' 不匹配任何命令，忽略
2025-08-01 09:27:34 | DEBUG | 收到消息: {'MsgId': 2038606960, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n那你还是用稳利宝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011659, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_3YCe158x|v1_5gcoOP/a</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 那你还是用稳利宝', 'NewMsgId': 3167631039148356527, 'MsgSeq': 871416308}
2025-08-01 09:27:34 | INFO | 收到文本消息: 消息ID:2038606960 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:那你还是用稳利宝
2025-08-01 09:27:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那你还是用稳利宝' from zll953369865 in 48097389945@chatroom
2025-08-01 09:27:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['那你还是用稳利宝']
2025-08-01 09:27:35 | DEBUG | 处理消息内容: '那你还是用稳利宝'
2025-08-01 09:27:35 | DEBUG | 消息内容 '那你还是用稳利宝' 不匹配任何命令，忽略
2025-08-01 09:27:40 | DEBUG | 收到消息: {'MsgId': 1205194910, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你害怕亏钱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011665, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_kVWdxoV+|v1_jeXdAfxC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你害怕亏钱', 'NewMsgId': 6656963140751841979, 'MsgSeq': 871416309}
2025-08-01 09:27:40 | INFO | 收到文本消息: 消息ID:1205194910 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你害怕亏钱
2025-08-01 09:27:40 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你害怕亏钱' from zll953369865 in 48097389945@chatroom
2025-08-01 09:27:40 | DEBUG | [DouBaoImageToImage] 命令解析: ['你害怕亏钱']
2025-08-01 09:27:40 | DEBUG | 处理消息内容: '你害怕亏钱'
2025-08-01 09:27:40 | DEBUG | 消息内容 '你害怕亏钱' 不匹配任何命令，忽略
2025-08-01 09:28:02 | DEBUG | 收到消息: {'MsgId': 1026992186, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这是干嘛？</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>43</type>\n\t\t\t<svrid>1031706493863117479</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zuoledd</chatusr>\n\t\t\t<displayname>作乐多端</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;5&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;weappsourceUsername&gt;(null),wxid_1q60hw5n1kkg12,zuoledd&lt;/weappsourceUsername&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;7eb233c8325eef642aea00b794cfdc83_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_ixUpJiM5|v1_JCQ8Mvpa&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>60:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754011582</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011688, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>7eb233c8325eef642aea00b794cfdc83_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_xRd/hlOR|v1_aSHsQTzO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 这是干嘛？', 'NewMsgId': 2418134431351337845, 'MsgSeq': 871416310}
2025-08-01 09:28:02 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-08-01 09:28:02 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:28:02 | INFO | 收到引用消息: 消息ID:1026992186 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:这是干嘛？ 引用类型:43
2025-08-01 09:28:03 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:28:03 | INFO | [DouBaoImageToImage] 消息内容: '这是干嘛？' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-01 09:28:03 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['这是干嘛？']
2025-08-01 09:28:03 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 09:28:03 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:28:03 | INFO |   - 消息内容: 这是干嘛？
2025-08-01 09:28:03 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 09:28:03 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-08-01 09:28:03 | INFO |   - 引用信息: {'MsgType': 43, 'Content': '60:0\n', 'Msgid': '1031706493863117479', 'NewMsgId': '1031706493863117479', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '作乐多端', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<weappsourceUsername>(null),wxid_1q60hw5n1kkg12,zuoledd</weappsourceUsername>\n\t<sec_msg_node>\n\t\t<uuid>7eb233c8325eef642aea00b794cfdc83_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_ixUpJiM5|v1_JCQ8Mvpa</signature>\n</msgsource>\n', 'Createtime': '1754011582', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-08-01 09:28:03 | INFO |   - 引用消息ID: 
2025-08-01 09:28:03 | INFO |   - 引用消息类型: 
2025-08-01 09:28:03 | INFO |   - 引用消息内容: 60:0

2025-08-01 09:28:03 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-08-01 09:28:10 | DEBUG | 收到消息: {'MsgId': 1361326927, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n人死了拉人去顶是吗？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011695, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_KZPjphm7|v1_OuOq7DgN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 人死了拉人去顶是吗？', 'NewMsgId': 3290427805294990546, 'MsgSeq': 871416311}
2025-08-01 09:28:10 | INFO | 收到文本消息: 消息ID:1361326927 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:人死了拉人去顶是吗？
2025-08-01 09:28:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '人死了拉人去顶是吗？' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-01 09:28:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['人死了拉人去顶是吗？']
2025-08-01 09:28:10 | DEBUG | 处理消息内容: '人死了拉人去顶是吗？'
2025-08-01 09:28:10 | DEBUG | 消息内容 '人死了拉人去顶是吗？' 不匹配任何命令，忽略
2025-08-01 09:28:22 | DEBUG | 收到消息: {'MsgId': 421951280, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n还是京牌'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011707, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_SF2StAIW|v1_5UPecUjc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 还是京牌', 'NewMsgId': 8471801813234224932, 'MsgSeq': 871416312}
2025-08-01 09:28:22 | INFO | 收到文本消息: 消息ID:421951280 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:还是京牌
2025-08-01 09:28:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '还是京牌' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-01 09:28:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['还是京牌']
2025-08-01 09:28:22 | DEBUG | 处理消息内容: '还是京牌'
2025-08-01 09:28:22 | DEBUG | 消息内容 '还是京牌' 不匹配任何命令，忽略
2025-08-01 09:29:02 | DEBUG | 收到消息: {'MsgId': 1993212331, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="0b84ec3f4fa30f1c36ab38fe83905f1e" len = "652098" productid="" androidmd5="0b84ec3f4fa30f1c36ab38fe83905f1e" androidlen="652098" s60v3md5 = "0b84ec3f4fa30f1c36ab38fe83905f1e" s60v3len="652098" s60v5md5 = "0b84ec3f4fa30f1c36ab38fe83905f1e" s60v5len="652098" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=0b84ec3f4fa30f1c36ab38fe83905f1e&amp;filekey=30440201010430302e02016e0402535a04203062383465633366346661333066316333366162333866653833393035663165020309f342040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268329cb00004c6de3d07d9280000006e01004fb1535a0fcfc0115684c7c50&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=2726fa8314d286b03bc2fb4b51ef3a25&amp;filekey=30440201010430302e02016e0402535a04203237323666613833313464323836623033626332666234623531656633613235020309f350040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268329cb00006079a3d07d9280000006e02004fb2535a0fcfc0115684c7c61&amp;ef=2&amp;bizid=1022" aeskey= "d52edceb53624b9ab497bed1a8f5eb69" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=be3125eec46952069368dcc3252eb7fb&amp;filekey=30440201010430302e02016e0402535a04206265333132356565633436393532303639333638646363333235326562376662020300f520040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268329cb0000712423d07d9280000006e03004fb3535a0fcfc0115684c7c7c&amp;ef=3&amp;bizid=1022" externmd5 = "a3b4c9271e6104b7a3bed2da033fd9eb" width= "400" height= "400" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011747, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_mK5qjXqQ|v1_HwKmpRWJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 603842009883335318, 'MsgSeq': 871416313}
2025-08-01 09:29:02 | INFO | 收到表情消息: 消息ID:1993212331 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:0b84ec3f4fa30f1c36ab38fe83905f1e 大小:652098
2025-08-01 09:29:02 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 603842009883335318
2025-08-01 09:29:04 | DEBUG | 收到消息: {'MsgId': 471855183, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>牛逼</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>6352337131112617991</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="d906b505d0bbb711ff108ec84d3a6e8d" encryver="1" cdnthumbaeskey="d906b505d0bbb711ff108ec84d3a6e8d" cdnthumburl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" cdnthumblength="3148" cdnthumbheight="120" cdnthumbwidth="95" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" length="9144" md5="ce14b78d0e961d488da97a57b3e70d9f" originsourcemd5="9112063822eedd35c66ad3380f87dcff"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836250878&lt;/sequence_id&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;b76fd857471cd9f115c11482bfadeb45_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="9144" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_Bkw5bbaT|v1_py/FsQ6C&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754011624</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_jegyk4i3v7zg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011749, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>2b5069789bb9e0ee76dad58490df02c8_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_Kditw8nI|v1_qlsUdd5r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 牛逼', 'NewMsgId': 4468248982820170406, 'MsgSeq': 871416314}
2025-08-01 09:29:04 | DEBUG | 从群聊消息中提取发送者: wxid_jegyk4i3v7zg22
2025-08-01 09:29:04 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:29:04 | INFO | 收到引用消息: 消息ID:471855183 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 内容:牛逼 引用类型:3
2025-08-01 09:29:04 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:29:04 | INFO | [DouBaoImageToImage] 消息内容: '牛逼' from wxid_jegyk4i3v7zg22 in 48097389945@chatroom
2025-08-01 09:29:04 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['牛逼']
2025-08-01 09:29:04 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 09:29:05 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:29:05 | INFO |   - 消息内容: 牛逼
2025-08-01 09:29:05 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 09:29:05 | INFO |   - 发送人: wxid_jegyk4i3v7zg22
2025-08-01 09:29:05 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>牛逼</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>6352337131112617991</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="d906b505d0bbb711ff108ec84d3a6e8d" encryver="1" cdnthumbaeskey="d906b505d0bbb711ff108ec84d3a6e8d" cdnthumburl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" cdnthumblength="3148" cdnthumbheight="120" cdnthumbwidth="95" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" length="9144" md5="ce14b78d0e961d488da97a57b3e70d9f" originsourcemd5="9112063822eedd35c66ad3380f87dcff"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836250878&lt;/sequence_id&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;b76fd857471cd9f115c11482bfadeb45_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="9144" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_Bkw5bbaT|v1_py/FsQ6C&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754011624</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_jegyk4i3v7zg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '6352337131112617991', 'NewMsgId': '6352337131112617991', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource><sequence_id>836250878</sequence_id>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>b76fd857471cd9f115c11482bfadeb45_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="9144" />\n\t<silence>1</silence>\n\t<membercount>72</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_Bkw5bbaT|v1_py/FsQ6C</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754011624', 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-08-01 09:29:05 | INFO |   - 引用消息ID: 
2025-08-01 09:29:05 | INFO |   - 引用消息类型: 
2025-08-01 09:29:05 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>牛逼</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>6352337131112617991</svrid>
			<fromusr>48097389945@chatroom</fromusr>
			<chatusr>xiaomaochong</chatusr>
			<displayname>小爱</displayname>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="d906b505d0bbb711ff108ec84d3a6e8d" encryver="1" cdnthumbaeskey="d906b505d0bbb711ff108ec84d3a6e8d" cdnthumburl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" cdnthumblength="3148" cdnthumbheight="120" cdnthumbwidth="95" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f6328e710204688c17e8042436636362373133652d613662332d343166312d623835372d333734363362386266643963020405250a020201000405004c4dfd00" length="9144" md5="ce14b78d0e961d488da97a57b3e70d9f" originsourcemd5="9112063822eedd35c66ad3380f87dcff"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836250878&lt;/sequence_id&gt;
	&lt;alnode&gt;
		&lt;fr&gt;2&lt;/fr&gt;
	&lt;/alnode&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;b76fd857471cd9f115c11482bfadeb45_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="9144" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;72&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_Bkw5bbaT|v1_py/FsQ6C&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1754011624</createtime>
		</refermsg>
	</appmsg>
	<fromusername>wxid_jegyk4i3v7zg22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-01 09:29:05 | INFO |   - 引用消息发送人: wxid_jegyk4i3v7zg22
2025-08-01 09:29:06 | DEBUG | 收到消息: {'MsgId': 681611865, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="8a0c6d3e5f8088e644f274ba364fb54d" len="1764845" productid="" androidmd5="8a0c6d3e5f8088e644f274ba364fb54d" androidlen="1764845" s60v3md5="8a0c6d3e5f8088e644f274ba364fb54d" s60v3len="1764845" s60v5md5="8a0c6d3e5f8088e644f274ba364fb54d" s60v5len="1764845" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=8a0c6d3e5f8088e644f274ba364fb54d&amp;filekey=30440201010430302e02016e040253480420386130633664336535663830383865363434663237346261333634666235346402031aeded040d00000004627466730000000132&amp;hy=SH&amp;storeid=26801008100050510d7cd29140000006e01004fb2534806b06bd1e6d3557ef&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8b9e25730f3b5600520de8a54021bdd&amp;filekey=30440201010430302e02016e040253480420633862396532353733306633623536303035323064653861353430323162646402031aedf0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268010081000773b5d7cd29140000006e02004fb2534806b06bd1e6d355819&amp;ef=2&amp;bizid=1022" aeskey="fe3bef3636374e7092450530557e1c32" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=e15ac63adf91296664a92286594c9142&amp;filekey=30440201010430302e02016e04025348042065313561633633616466393132393636363461393232383635393463393134320203014fa0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268010081000a677dd7cd29140000006e03004fb3534806b06bd1e6d35584f&amp;ef=3&amp;bizid=1022" externmd5="867dc11eae53059141468842aa26ed36" width="300" height="299" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011752, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_5kxZoZ+K|v1_qdFXVtls</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 6130089928253967705, 'MsgSeq': 871416315}
2025-08-01 09:29:06 | INFO | 收到表情消息: 消息ID:681611865 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:8a0c6d3e5f8088e644f274ba364fb54d 大小:1764845
2025-08-01 09:29:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6130089928253967705
2025-08-01 09:29:11 | DEBUG | 收到消息: {'MsgId': 1423296047, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n我刚拿出来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011756, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_NEva7J+B|v1_5UUa630A</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 我刚拿出来', 'NewMsgId': 5918076081784970845, 'MsgSeq': 871416316}
2025-08-01 09:29:11 | INFO | 收到文本消息: 消息ID:1423296047 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:我刚拿出来
2025-08-01 09:29:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我刚拿出来' from wxid_jegyk4i3v7zg22 in 48097389945@chatroom
2025-08-01 09:29:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['我刚拿出来']
2025-08-01 09:29:12 | DEBUG | 处理消息内容: '我刚拿出来'
2025-08-01 09:29:12 | DEBUG | 消息内容 '我刚拿出来' 不匹配任何命令，忽略
2025-08-01 09:29:17 | DEBUG | 收到消息: {'MsgId': 2126498917, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n这群里最真的就是小爱和我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011762, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_DjOzR6BC|v1_QxgU2w0y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 这群里最真的就是小爱和我', 'NewMsgId': 4085959653445308174, 'MsgSeq': 871416317}
2025-08-01 09:29:17 | INFO | 收到文本消息: 消息ID:2126498917 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:这群里最真的就是小爱和我
2025-08-01 09:29:17 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这群里最真的就是小爱和我' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-01 09:29:17 | DEBUG | [DouBaoImageToImage] 命令解析: ['这群里最真的就是小爱和我']
2025-08-01 09:29:17 | DEBUG | 处理消息内容: '这群里最真的就是小爱和我'
2025-08-01 09:29:17 | DEBUG | 消息内容 '这群里最真的就是小爱和我' 不匹配任何命令，忽略
2025-08-01 09:29:28 | DEBUG | 收到消息: {'MsgId': 122721593, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zuoledd:\n这群里最真的就是小爱和我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011774, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>12</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_VUvzKqAb|v1_VvJzNEMM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '作乐多端 : 这群里最真的就是小爱和我', 'NewMsgId': 7023762415949758229, 'MsgSeq': 871416318}
2025-08-01 09:29:28 | INFO | 收到文本消息: 消息ID:122721593 来自:48097389945@chatroom 发送人:zuoledd @:[] 内容:这群里最真的就是小爱和我
2025-08-01 09:29:29 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这群里最真的就是小爱和我' from zuoledd in 48097389945@chatroom
2025-08-01 09:29:29 | DEBUG | [DouBaoImageToImage] 命令解析: ['这群里最真的就是小爱和我']
2025-08-01 09:29:29 | DEBUG | 处理消息内容: '这群里最真的就是小爱和我'
2025-08-01 09:29:29 | DEBUG | 消息内容 '这群里最真的就是小爱和我' 不匹配任何命令，忽略
