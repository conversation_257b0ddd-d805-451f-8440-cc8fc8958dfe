2025-08-01 10:05:21 | SUCCESS | 读取主设置成功
2025-08-01 10:05:21 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 10:05:21 | INFO | 2025/08/01 10:05:21 GetRedisAddr: 127.0.0.1:6379
2025-08-01 10:05:21 | INFO | 2025/08/01 10:05:21 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 10:05:21 | INFO | 2025/08/01 10:05:21 Server start at :9000
2025-08-01 10:05:22 | SUCCESS | WechatAPI服务已启动
2025-08-01 10:05:22 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 10:05:22 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 10:05:22 | SUCCESS | 登录成功
2025-08-01 10:05:22 | SUCCESS | 已开启自动心跳
2025-08-01 10:05:22 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 10:05:22 | SUCCESS | 数据库初始化成功
2025-08-01 10:05:22 | SUCCESS | 定时任务已启动
2025-08-01 10:05:22 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 10:05:22 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:05:23 | INFO | 播客API初始化成功
2025-08-01 10:05:23 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 10:05:23 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 10:05:23 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 10:05:23 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 10:05:23 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 10:05:23 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 10:05:23 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 10:05:23 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 10:05:23 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 10:05:23 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 10:05:23 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 10:05:23 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 10:05:23 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 10:05:23 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 10:05:23 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 10:05:23 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 10:05:23 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 10:05:23 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 10:05:23 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 10:05:23 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 10:05:23 | DEBUG |   - 启用状态: True
2025-08-01 10:05:23 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 10:05:23 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 10:05:23 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 10:05:23 | DEBUG |   - Cookies配置: 已配置
2025-08-01 10:05:23 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 10:05:23 | DEBUG |   - 自然化响应: True
2025-08-01 10:05:23 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 10:05:23 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-01 10:05:23 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 10:05:23 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 10:05:23 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 10:05:23 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 10:05:23 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 10:05:23 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:05:23 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 10:05:23 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 10:05:23 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 10:05:23 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 10:05:23 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 10:05:23 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 10:05:23 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 10:05:24 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 10:05:24 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 10:05:24 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 10:05:24 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 10:05:25 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 10:05:25 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:05:25 | INFO | [yuanbao] 插件初始化完成
2025-08-01 10:05:25 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 10:05:25 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 10:05:25 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 10:05:25 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 10:05:25 | INFO | 处理堆积消息中
2025-08-01 10:05:25 | SUCCESS | 处理堆积消息完毕
2025-08-01 10:05:25 | SUCCESS | 开始处理消息
2025-08-01 10:05:35 | DEBUG | 收到消息: {'MsgId': 200700046, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'Edison-w:\n两个版本能共存，爽歪歪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013941, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_bavFmdty|v1_MC5KcrVz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\x7f\x7f\x7f\x7f麦来乱 : 两个版本能共存，爽歪歪', 'NewMsgId': 3810178088017650482, 'MsgSeq': 871416379}
2025-08-01 10:05:35 | INFO | 收到文本消息: 消息ID:200700046 来自:47325400669@chatroom 发送人:Edison-w @:[] 内容:两个版本能共存，爽歪歪
2025-08-01 10:05:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '两个版本能共存，爽歪歪' from Edison-w in 47325400669@chatroom
2025-08-01 10:05:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['两个版本能共存，爽歪歪']
2025-08-01 10:05:35 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 10:05:35 | DEBUG | 处理消息内容: '两个版本能共存，爽歪歪'
2025-08-01 10:05:35 | DEBUG | 消息内容 '两个版本能共存，爽歪歪' 不匹配任何命令，忽略
2025-08-01 10:05:51 | DEBUG | 收到消息: {'MsgId': 48375611, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>牛逼</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>1911722274363922564</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>last--exile</chatusr>\n\t\t\t<createtime>1754013892</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;alnode&gt;\n        &lt;fr&gt;2&lt;/fr&gt;\n    &lt;/alnode&gt;\n    &lt;sec_msg_node&gt;\n        &lt;uuid&gt;21a8b889df0f6099175344a152b511cd_&lt;/uuid&gt;\n        &lt;risk-file-flag /&gt;\n        &lt;risk-file-md5-list /&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;imgmsg_pd cdnmidimgurl_size="63267" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;72&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_lFOFHgUT|v1_0Y+86qVH&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="cd1e9934d7ac241facc7a3291b2477e4" encryver="1" cdnthumbaeskey="cd1e9934d7ac241facc7a3291b2477e4" cdnthumburl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" cdnthumblength="4327" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" length="63267" md5="64c998ceb593e05f95b622b5b23b4931" hevc_mid_size="63267" originsourcemd5="d5b3b3421c088fb5cb9fea16035af50d"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<displayname>亮</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013956, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>1085d39c4913dfcf175e3bef09df1f9a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_lQ6e6QFX|v1_SOjGSpFj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 牛逼', 'NewMsgId': 6405318646715170772, 'MsgSeq': 871416380}
2025-08-01 10:05:51 | DEBUG | 从群聊消息中提取发送者: zll953369865
2025-08-01 10:05:51 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 10:05:51 | INFO | 收到引用消息: 消息ID:48375611 来自:48097389945@chatroom 发送人:zll953369865 内容:牛逼 引用类型:3
2025-08-01 10:05:51 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 10:05:51 | INFO | [DouBaoImageToImage] 消息内容: '牛逼' from zll953369865 in 48097389945@chatroom
2025-08-01 10:05:51 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['牛逼']
2025-08-01 10:05:51 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 10:05:51 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 10:05:51 | INFO |   - 消息内容: 牛逼
2025-08-01 10:05:51 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 10:05:51 | INFO |   - 发送人: zll953369865
2025-08-01 10:05:51 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>牛逼</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>1911722274363922564</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>last--exile</chatusr>\n\t\t\t<createtime>1754013892</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;alnode&gt;\n        &lt;fr&gt;2&lt;/fr&gt;\n    &lt;/alnode&gt;\n    &lt;sec_msg_node&gt;\n        &lt;uuid&gt;21a8b889df0f6099175344a152b511cd_&lt;/uuid&gt;\n        &lt;risk-file-flag /&gt;\n        &lt;risk-file-md5-list /&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;imgmsg_pd cdnmidimgurl_size="63267" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;72&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_lFOFHgUT|v1_0Y+86qVH&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="cd1e9934d7ac241facc7a3291b2477e4" encryver="1" cdnthumbaeskey="cd1e9934d7ac241facc7a3291b2477e4" cdnthumburl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" cdnthumblength="4327" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" length="63267" md5="64c998ceb593e05f95b622b5b23b4931" hevc_mid_size="63267" originsourcemd5="d5b3b3421c088fb5cb9fea16035af50d"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<displayname>亮</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '1911722274363922564', 'NewMsgId': '1911722274363922564', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '亮', 'MsgSource': '<msgsource>\n    <alnode>\n        <fr>2</fr>\n    </alnode>\n    <sec_msg_node>\n        <uuid>21a8b889df0f6099175344a152b511cd_</uuid>\n        <risk-file-flag />\n        <risk-file-md5-list />\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <imgmsg_pd cdnmidimgurl_size="63267" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" />\n    <silence>1</silence>\n    <membercount>72</membercount>\n    <signature>N0_V1_lFOFHgUT|v1_0Y+86qVH</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1754013892', 'SenderWxid': 'zll953369865'}
2025-08-01 10:05:51 | INFO |   - 引用消息ID: 
2025-08-01 10:05:51 | INFO |   - 引用消息类型: 
2025-08-01 10:05:51 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>牛逼</title>
		<des />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<dataurl />
		<lowurl />
		<lowdataurl />
		<recorditem />
		<thumburl />
		<messageaction />
		<laninfo />
		<refermsg>
			<type>3</type>
			<svrid>1911722274363922564</svrid>
			<fromusr>48097389945@chatroom</fromusr>
			<chatusr>last--exile</chatusr>
			<createtime>1754013892</createtime>
			<msgsource>&lt;msgsource&gt;
    &lt;alnode&gt;
        &lt;fr&gt;2&lt;/fr&gt;
    &lt;/alnode&gt;
    &lt;sec_msg_node&gt;
        &lt;uuid&gt;21a8b889df0f6099175344a152b511cd_&lt;/uuid&gt;
        &lt;risk-file-flag /&gt;
        &lt;risk-file-md5-list /&gt;
        &lt;alnode&gt;
            &lt;fr&gt;1&lt;/fr&gt;
        &lt;/alnode&gt;
    &lt;/sec_msg_node&gt;
    &lt;imgmsg_pd cdnmidimgurl_size="63267" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;
    &lt;silence&gt;1&lt;/silence&gt;
    &lt;membercount&gt;72&lt;/membercount&gt;
    &lt;signature&gt;N0_V1_lFOFHgUT|v1_0Y+86qVH&lt;/signature&gt;
    &lt;tmp_node&gt;
        &lt;publisher-id /&gt;
    &lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="cd1e9934d7ac241facc7a3291b2477e4" encryver="1" cdnthumbaeskey="cd1e9934d7ac241facc7a3291b2477e4" cdnthumburl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" cdnthumblength="4327" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" length="63267" md5="64c998ceb593e05f95b622b5b23b4931" hevc_mid_size="63267" originsourcemd5="d5b3b3421c088fb5cb9fea16035af50d"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<displayname>亮</displayname>
		</refermsg>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<commenturl />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<aeskey />
		</appattach>
		<webviewshared>
			<publisherId />
			<publisherReqId>0</publisherReqId>
		</webviewshared>
		<weappinfo>
			<pagepath />
			<username />
			<appid />
			<appservicetype>0</appservicetype>
		</weappinfo>
		<websearch />
	</appmsg>
	<fromusername>zll953369865</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 10:05:51 | INFO |   - 引用消息发送人: zll953369865
2025-08-01 10:08:33 | DEBUG | 收到消息: {'MsgId': 1885942795, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14713246167702903030</objectId>\n\t\t\t<objectNonceId>4240654220264674679_4_20_13_1_1754013687666225_6f5b0b80-6e7b-11f0-a487-a957b245ab8c</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>小香蕉的影视</nickname>\n\t\t\t<username>v2_060000231003b20faec8c6eb8119c4d4cf0deb3cb07712bfd1307b6906990f5d9c6e493884db@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/jjZmloiaeqMeGUTfN8Q4fxLDO90uTT6ibNsiazvMHujiaI3P93WTSvhxicO9ObztoZgFcaX0kZeibpNoMKGprnEo2XSTNjYWKSeibt5dHhia1nmxOd0/0]]></avatar>\n\t\t\t<desc>这个笑点不断的电影估计很多人都没有看过，太有意思了#影视剪辑#搞笑视频</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQApkoOOicOg80mR1sYia9tJbX1OSMfKArat44deC2AaAPjqshEdM9dDANsHGfZY7KgYtkdkvHunmsSMicpWIydjDKS&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a15c&token=6xykWLEnztJD1V02HxcJfcF9CUjFJDXfPNEib6YYJpDF6oNMGUouKEvc6v5QS9mNX0nvSo4O5uxX4KJREicXk5slm1CQVveQAvDLTsDC0gHdsbt0afoDWaPEJS2eglVxr6d3IuRpOTicibB5lfKnkialSk1iaqryNN4kzNp2xW1ae2QVk&basedata=CAESBnhXVDE1NhoGeFdUMTExGgZ4V1QxMTIaBnhXVDE1NhoGeFdUMTEzGgZ4V1QxNTcaBnhXVDE1OCIYCgoKBnhXVDExMhABCgoKBnhXVDE1NxABKgcIvR4QABgC&sign=tR-a35UJS3S5LpN9hCxCYB3r1z_7B1GlteFBAT4fZFdzoXqvUdcLaOy2SLt8VCFNeCran4ARKWsyvfdw2e9_Pw&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAADQUql9nveqCwQl%2Bh%2BMaBAAAADnaHZTnGbFfAj9RgZXfw6VmVOk83whQm6bp4ZiP6ummFSavOq4gkfJW0S6PDgXicwNHdZi6xRg7FI%3D&svrnonce=1754013690]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz9BZg63NA0rtiaNEVfsGNBQ2NTKm3XRlyQg974blYe7KaULTBjDKR8kA5Ip0FCYCcI04OENoTicy2FpVaXiazIlvag&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhhcl4xvp1WvDUiadTXO3c6wUghEdg5YCQtGiawhfblYYNobuX5agYx0cgQ5otibr7MkPD1pnr9GuO6tKWcSBqt0G8j56N0UPbmfuZoxdq3aEIub6j4MJFovbZnvRGgMJOYR6H3SQZUyvfj&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz9BZg63NA0rtiaNEVfsGNBQ2NTKm3XRlyQg974blYe7KaULTBjDKR8kA5Ip0FCYCcI04OENoTicy2FpVaXiazIlvag&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhhcl4xvp1WvDUiadTXO3c6wUghEdg5YCQtGiawhfblYYNobuX5agYx0cgQ5otibr7MkPD1pnr9GuO6tKWcSBqt0G8j56N0UPbmfuZoxdq3aEIub6j4MJFovbZnvRGgMJOYR6H3SQZUyvfj&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1920.0</width>\n\t\t\t\t\t<height>1080.0</height>\n\t\t\t\t\t<videoPlayDuration>254</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754013689789","contextId":"1-1-20-e6647cbcc0d94f33875606aaa13aaf19","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014118, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>356a758d12074b8d075f89237373913f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_Cviceno5|v1_qPwtf1il</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 2411956572053468686, 'MsgSeq': 871416381}
2025-08-01 10:08:33 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-08-01 10:08:33 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713246167702903030</objectId>
			<objectNonceId>4240654220264674679_4_20_13_1_1754013687666225_6f5b0b80-6e7b-11f0-a487-a957b245ab8c</objectNonceId>
			<feedType>4</feedType>
			<nickname>小香蕉的影视</nickname>
			<username>v2_060000231003b20faec8c6eb8119c4d4cf0deb3cb07712bfd1307b6906990f5d9c6e493884db@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/jjZmloiaeqMeGUTfN8Q4fxLDO90uTT6ibNsiazvMHujiaI3P93WTSvhxicO9ObztoZgFcaX0kZeibpNoMKGprnEo2XSTNjYWKSeibt5dHhia1nmxOd0/0]]></avatar>
			<desc>这个笑点不断的电影估计很多人都没有看过，太有意思了#影视剪辑#搞笑视频</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQApkoOOicOg80mR1sYia9tJbX1OSMfKArat44deC2AaAPjqshEdM9dDANsHGfZY7KgYtkdkvHunmsSMicpWIydjDKS&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a15c&token=6xykWLEnztJD1V02HxcJfcF9CUjFJDXfPNEib6YYJpDF6oNMGUouKEvc6v5QS9mNX0nvSo4O5uxX4KJREicXk5slm1CQVveQAvDLTsDC0gHdsbt0afoDWaPEJS2eglVxr6d3IuRpOTicibB5lfKnkialSk1iaqryNN4kzNp2xW1ae2QVk&basedata=CAESBnhXVDE1NhoGeFdUMTExGgZ4V1QxMTIaBnhXVDE1NhoGeFdUMTEzGgZ4V1QxNTcaBnhXVDE1OCIYCgoKBnhXVDExMhABCgoKBnhXVDE1NxABKgcIvR4QABgC&sign=tR-a35UJS3S5LpN9hCxCYB3r1z_7B1GlteFBAT4fZFdzoXqvUdcLaOy2SLt8VCFNeCran4ARKWsyvfdw2e9_Pw&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAADQUql9nveqCwQl%2Bh%2BMaBAAAADnaHZTnGbFfAj9RgZXfw6VmVOk83whQm6bp4ZiP6ummFSavOq4gkfJW0S6PDgXicwNHdZi6xRg7FI%3D&svrnonce=1754013690]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz9BZg63NA0rtiaNEVfsGNBQ2NTKm3XRlyQg974blYe7KaULTBjDKR8kA5Ip0FCYCcI04OENoTicy2FpVaXiazIlvag&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhhcl4xvp1WvDUiadTXO3c6wUghEdg5YCQtGiawhfblYYNobuX5agYx0cgQ5otibr7MkPD1pnr9GuO6tKWcSBqt0G8j56N0UPbmfuZoxdq3aEIub6j4MJFovbZnvRGgMJOYR6H3SQZUyvfj&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz9BZg63NA0rtiaNEVfsGNBQ2NTKm3XRlyQg974blYe7KaULTBjDKR8kA5Ip0FCYCcI04OENoTicy2FpVaXiazIlvag&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhhcl4xvp1WvDUiadTXO3c6wUghEdg5YCQtGiawhfblYYNobuX5agYx0cgQ5otibr7MkPD1pnr9GuO6tKWcSBqt0G8j56N0UPbmfuZoxdq3aEIub6j4MJFovbZnvRGgMJOYR6H3SQZUyvfj&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1920.0</width>
					<height>1080.0</height>
					<videoPlayDuration>254</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754013689789","contextId":"1-1-20-e6647cbcc0d94f33875606aaa13aaf19","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 10:08:33 | DEBUG | XML消息类型: 51
2025-08-01 10:08:33 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-01 10:08:33 | DEBUG | XML消息描述: None
2025-08-01 10:08:33 | DEBUG | 附件信息 totallen: 0
2025-08-01 10:08:33 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-01 10:08:33 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-01 10:08:33 | INFO | 未知的XML消息类型: 51
2025-08-01 10:08:33 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-01 10:08:33 | INFO | 消息描述: None
2025-08-01 10:08:33 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-01 10:08:33 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713246167702903030</objectId>
			<objectNonceId>4240654220264674679_4_20_13_1_1754013687666225_6f5b0b80-6e7b-11f0-a487-a957b245ab8c</objectNonceId>
			<feedType>4</feedType>
			<nickname>小香蕉的影视</nickname>
			<username>v2_060000231003b20faec8c6eb8119c4d4cf0deb3cb07712bfd1307b6906990f5d9c6e493884db@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/jjZmloiaeqMeGUTfN8Q4fxLDO90uTT6ibNsiazvMHujiaI3P93WTSvhxicO9ObztoZgFcaX0kZeibpNoMKGprnEo2XSTNjYWKSeibt5dHhia1nmxOd0/0]]></avatar>
			<desc>这个笑点不断的电影估计很多人都没有看过，太有意思了#影视剪辑#搞笑视频</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQApkoOOicOg80mR1sYia9tJbX1OSMfKArat44deC2AaAPjqshEdM9dDANsHGfZY7KgYtkdkvHunmsSMicpWIydjDKS&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a15c&token=6xykWLEnztJD1V02HxcJfcF9CUjFJDXfPNEib6YYJpDF6oNMGUouKEvc6v5QS9mNX0nvSo4O5uxX4KJREicXk5slm1CQVveQAvDLTsDC0gHdsbt0afoDWaPEJS2eglVxr6d3IuRpOTicibB5lfKnkialSk1iaqryNN4kzNp2xW1ae2QVk&basedata=CAESBnhXVDE1NhoGeFdUMTExGgZ4V1QxMTIaBnhXVDE1NhoGeFdUMTEzGgZ4V1QxNTcaBnhXVDE1OCIYCgoKBnhXVDExMhABCgoKBnhXVDE1NxABKgcIvR4QABgC&sign=tR-a35UJS3S5LpN9hCxCYB3r1z_7B1GlteFBAT4fZFdzoXqvUdcLaOy2SLt8VCFNeCran4ARKWsyvfdw2e9_Pw&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAADQUql9nveqCwQl%2Bh%2BMaBAAAADnaHZTnGbFfAj9RgZXfw6VmVOk83whQm6bp4ZiP6ummFSavOq4gkfJW0S6PDgXicwNHdZi6xRg7FI%3D&svrnonce=1754013690]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz9BZg63NA0rtiaNEVfsGNBQ2NTKm3XRlyQg974blYe7KaULTBjDKR8kA5Ip0FCYCcI04OENoTicy2FpVaXiazIlvag&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhhcl4xvp1WvDUiadTXO3c6wUghEdg5YCQtGiawhfblYYNobuX5agYx0cgQ5otibr7MkPD1pnr9GuO6tKWcSBqt0G8j56N0UPbmfuZoxdq3aEIub6j4MJFovbZnvRGgMJOYR6H3SQZUyvfj&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqz9BZg63NA0rtiaNEVfsGNBQ2NTKm3XRlyQg974blYe7KaULTBjDKR8kA5Ip0FCYCcI04OENoTicy2FpVaXiazIlvag&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhhcl4xvp1WvDUiadTXO3c6wUghEdg5YCQtGiawhfblYYNobuX5agYx0cgQ5otibr7MkPD1pnr9GuO6tKWcSBqt0G8j56N0UPbmfuZoxdq3aEIub6j4MJFovbZnvRGgMJOYR6H3SQZUyvfj&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1920.0</width>
					<height>1080.0</height>
					<videoPlayDuration>254</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754013689789","contextId":"1-1-20-e6647cbcc0d94f33875606aaa13aaf19","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 10:10:03 | DEBUG | 收到消息: {'MsgId': 17269750, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n@【微信红包】收到一个微信红包\u2005把你的车钥匙app截图给我来一张'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014209, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[zll953369865]]></atuserlist>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>16</inlenlist>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_KklPqd8T|v1_iy4TY4Zy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : @【微信红包】收到一个微信红包\u2005把你的车钥匙app截图给我来一张', 'NewMsgId': 6761262625064966088, 'MsgSeq': 871416382}
2025-08-01 10:10:03 | INFO | 收到文本消息: 消息ID:17269750 来自:48097389945@chatroom 发送人:xiaomaochong @:['zll953369865'] 内容:@【微信红包】收到一个微信红包 把你的车钥匙app截图给我来一张
2025-08-01 10:10:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@【微信红包】收到一个微信红包 把你的车钥匙app截图给我来一张' from xiaomaochong in 48097389945@chatroom
2025-08-01 10:10:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['@【微信红包】收到一个微信红包\u2005把你的车钥匙app截图给我来一张']
2025-08-01 10:10:04 | DEBUG | 处理消息内容: '@【微信红包】收到一个微信红包 把你的车钥匙app截图给我来一张'
2025-08-01 10:10:04 | DEBUG | 消息内容 '@【微信红包】收到一个微信红包 把你的车钥匙app截图给我来一张' 不匹配任何命令，忽略
2025-08-01 10:10:35 | DEBUG | 收到消息: {'MsgId': 10580302, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n我知道了 你想加入奔驰群 需要截图'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014241, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_jeuK72PP|v1_55tx/0QN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 我知道了 你想加入奔驰群 需要截图', 'NewMsgId': 1924603856119083926, 'MsgSeq': 871416383}
2025-08-01 10:10:35 | INFO | 收到文本消息: 消息ID:10580302 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:我知道了 你想加入奔驰群 需要截图
2025-08-01 10:10:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我知道了 你想加入奔驰群 需要截图' from zll953369865 in 48097389945@chatroom
2025-08-01 10:10:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['我知道了', '你想加入奔驰群', '需要截图']
2025-08-01 10:10:36 | DEBUG | 处理消息内容: '我知道了 你想加入奔驰群 需要截图'
2025-08-01 10:10:36 | DEBUG | 消息内容 '我知道了 你想加入奔驰群 需要截图' 不匹配任何命令，忽略
2025-08-01 10:10:46 | DEBUG | 收到消息: {'MsgId': 1833617933, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你自己去百度上搜'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014251, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_dWcvo6Su|v1_LUmcqv23</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你自己去百度上搜', 'NewMsgId': 7861334751788912790, 'MsgSeq': 871416384}
2025-08-01 10:10:46 | INFO | 收到文本消息: 消息ID:1833617933 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你自己去百度上搜
2025-08-01 10:10:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你自己去百度上搜' from zll953369865 in 48097389945@chatroom
2025-08-01 10:10:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['你自己去百度上搜']
2025-08-01 10:10:46 | DEBUG | 处理消息内容: '你自己去百度上搜'
2025-08-01 10:10:46 | DEBUG | 消息内容 '你自己去百度上搜' 不匹配任何命令，忽略
2025-08-01 10:10:58 | DEBUG | 收到消息: {'MsgId': 1493242208, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n@浅棠云雾\u2005因为玩好了，你可以把糖果玩其他的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014263, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ohq9p1qosjzq22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_NOYnFxPx|v1_5fXM2Vjw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4873067175747393426, 'MsgSeq': 871416385}
2025-08-01 10:10:58 | INFO | 收到文本消息: 消息ID:1493242208 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:['wxid_ohq9p1qosjzq22'] 内容:@浅棠云雾 因为玩好了，你可以把糖果玩其他的
2025-08-01 10:10:58 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@浅棠云雾 因为玩好了，你可以把糖果玩其他的' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-01 10:10:58 | DEBUG | [DouBaoImageToImage] 命令解析: ['@浅棠云雾\u2005因为玩好了，你可以把糖果玩其他的']
2025-08-01 10:10:58 | DEBUG | 处理消息内容: '@浅棠云雾 因为玩好了，你可以把糖果玩其他的'
2025-08-01 10:10:58 | DEBUG | 消息内容 '@浅棠云雾 因为玩好了，你可以把糖果玩其他的' 不匹配任何命令，忽略
2025-08-01 10:11:00 | DEBUG | 收到消息: {'MsgId': 910511514, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qq631390473:\n@慕ؓ悦ؓ˒\u2005换个狗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014265, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_2530z9t0joek22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_JbrwCzV+|v1_GDkvUcyE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6525579369298903429, 'MsgSeq': 871416386}
2025-08-01 10:11:00 | INFO | 收到文本消息: 消息ID:910511514 来自:27852221909@chatroom 发送人:qq631390473 @:['wxid_2530z9t0joek22'] 内容:@慕ؓ悦ؓ˒ 换个狗
2025-08-01 10:11:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@慕ؓ悦ؓ˒ 换个狗' from qq631390473 in 27852221909@chatroom
2025-08-01 10:11:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['@慕ؓ悦ؓ˒\u2005换个狗']
2025-08-01 10:11:00 | DEBUG | 处理消息内容: '@慕ؓ悦ؓ˒ 换个狗'
2025-08-01 10:11:00 | DEBUG | 消息内容 '@慕ؓ悦ؓ˒ 换个狗' 不匹配任何命令，忽略
2025-08-01 10:11:14 | DEBUG | 收到消息: {'MsgId': 165281489, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n我是想进小米群，看能不能混进去'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014280, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_R0Qnt8Zs|v1_IaetDf3i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 我是想进小米群，看能不能混进去', 'NewMsgId': 3994833770773062385, 'MsgSeq': 871416387}
2025-08-01 10:11:14 | INFO | 收到文本消息: 消息ID:165281489 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:我是想进小米群，看能不能混进去
2025-08-01 10:11:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我是想进小米群，看能不能混进去' from xiaomaochong in 48097389945@chatroom
2025-08-01 10:11:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['我是想进小米群，看能不能混进去']
2025-08-01 10:11:15 | DEBUG | 处理消息内容: '我是想进小米群，看能不能混进去'
2025-08-01 10:11:15 | DEBUG | 消息内容 '我是想进小米群，看能不能混进去' 不匹配任何命令，忽略
2025-08-01 10:11:35 | DEBUG | 收到消息: {'MsgId': 1383130660, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014301, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_qX6lY5g4|v1_gExKGXVm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : [破涕为笑]', 'NewMsgId': 252200920755930185, 'MsgSeq': 871416388}
2025-08-01 10:11:35 | INFO | 收到表情消息: 消息ID:1383130660 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:[破涕为笑]
2025-08-01 10:11:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 252200920755930185
2025-08-01 10:11:53 | DEBUG | 收到消息: {'MsgId': 154368995, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n快把你的截图给我一个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014318, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>16</inlenlist>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_zkVal8jl|v1_XjK6XhxU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 快把你的截图给我一个', 'NewMsgId': 8662481904263082114, 'MsgSeq': 871416389}
2025-08-01 10:11:53 | INFO | 收到文本消息: 消息ID:154368995 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:快把你的截图给我一个
2025-08-01 10:11:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '快把你的截图给我一个' from xiaomaochong in 48097389945@chatroom
2025-08-01 10:11:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['快把你的截图给我一个']
2025-08-01 10:11:53 | DEBUG | 处理消息内容: '快把你的截图给我一个'
2025-08-01 10:11:53 | DEBUG | 消息内容 '快把你的截图给我一个' 不匹配任何命令，忽略
2025-08-01 10:11:59 | DEBUG | 收到消息: {'MsgId': 534144775, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n没有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014324, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_s38GjlL5|v1_GJZaaz3H</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 没有', 'NewMsgId': 7092625840139870232, 'MsgSeq': 871416390}
2025-08-01 10:11:59 | INFO | 收到文本消息: 消息ID:534144775 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:没有
2025-08-01 10:11:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没有' from zll953369865 in 48097389945@chatroom
2025-08-01 10:11:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['没有']
2025-08-01 10:11:59 | DEBUG | 处理消息内容: '没有'
2025-08-01 10:11:59 | DEBUG | 消息内容 '没有' 不匹配任何命令，忽略
2025-08-01 10:12:01 | DEBUG | 收到消息: {'MsgId': 2082807245, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n去百度上搜吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014327, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_E2KUg9EW|v1_QvG8nYux</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 去百度上搜吧', 'NewMsgId': 5450739775360089383, 'MsgSeq': 871416391}
2025-08-01 10:12:01 | INFO | 收到文本消息: 消息ID:2082807245 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:去百度上搜吧
2025-08-01 10:12:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '去百度上搜吧' from zll953369865 in 48097389945@chatroom
2025-08-01 10:12:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['去百度上搜吧']
2025-08-01 10:12:02 | DEBUG | 处理消息内容: '去百度上搜吧'
2025-08-01 10:12:02 | DEBUG | 消息内容 '去百度上搜吧' 不匹配任何命令，忽略
2025-08-01 10:12:03 | DEBUG | 收到消息: {'MsgId': 1532533086, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="fecf53747e504667b7bc126a701108d5" encryver="1" cdnthumbaeskey="fecf53747e504667b7bc126a701108d5" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204633122750204688c2276042439303531306163312d633031372d346236622d383261372d346235666237623661613538020405250a020201000405004c543d00" cdnthumblength="7789" cdnthumbheight="120" cdnthumbwidth="81" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204633122750204688c2276042439303531306163312d633031372d346236622d383261372d346235666237623661613538020405250a020201000405004c543d00" length="230537" md5="3143e3d787165d08e87ff5e32476bb2f" originsourcemd5="a7bcdaf1e0a3d2d3ad6f2b6e1af7ca90">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014327, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>0ecfe5295f1e2a9c429d9d746e67ae65_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_78+hwtOP|v1_OY9DlybG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 5627926513882776179, 'MsgSeq': 871416392}
2025-08-01 10:12:03 | INFO | 收到图片消息: 消息ID:1532533086 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="fecf53747e504667b7bc126a701108d5" encryver="1" cdnthumbaeskey="fecf53747e504667b7bc126a701108d5" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204633122750204688c2276042439303531306163312d633031372d346236622d383261372d346235666237623661613538020405250a020201000405004c543d00" cdnthumblength="7789" cdnthumbheight="120" cdnthumbwidth="81" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204633122750204688c2276042439303531306163312d633031372d346236622d383261372d346235666237623661613538020405250a020201000405004c543d00" length="230537" md5="3143e3d787165d08e87ff5e32476bb2f" originsourcemd5="a7bcdaf1e0a3d2d3ad6f2b6e1af7ca90"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 10:12:04 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 10:12:04 | INFO | [TimerTask] 缓存图片消息: 1532533086
2025-08-01 10:12:29 | DEBUG | 收到消息: {'MsgId': 1712714112, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n小米yu7的车友我没有[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014354, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_DF0HbRJa|v1_2ijHjRTn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 小米yu7的车友我没有[捂脸]', 'NewMsgId': 8987962111953418764, 'MsgSeq': 871416393}
2025-08-01 10:12:29 | INFO | 收到文本消息: 消息ID:1712714112 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:小米yu7的车友我没有[捂脸]
2025-08-01 10:12:29 | DEBUG | [DouBaoImageToImage] 收到文本消息: '小米yu7的车友我没有[捂脸]' from xiaomaochong in 48097389945@chatroom
2025-08-01 10:12:29 | DEBUG | [DouBaoImageToImage] 命令解析: ['小米yu7的车友我没有[捂脸]']
2025-08-01 10:12:29 | DEBUG | 处理消息内容: '小米yu7的车友我没有[捂脸]'
2025-08-01 10:12:29 | DEBUG | 消息内容 '小米yu7的车友我没有[捂脸]' 不匹配任何命令，忽略
2025-08-01 10:13:04 | DEBUG | 收到消息: {'MsgId': 1383095806, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>百度上的时间不对</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5450739775360089383</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zll953369865</chatusr>\n\t\t\t<displayname>【微信红包】收到一个微信红包</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;alnode&gt;\n\t\t\t&lt;fr&gt;1&lt;/fr&gt;\n\t\t&lt;/alnode&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_LPZjZ3Je|v1_SZIsaBDl&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n去百度上搜吧</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754014327</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014390, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>88887169eb81b95ef0bb1172df53320e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_ViBURror|v1_719n+HHj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 百度上的时间不对', 'NewMsgId': 5276657161907375876, 'MsgSeq': 871416394}
2025-08-01 10:13:04 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-08-01 10:13:04 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 10:13:04 | INFO | 收到引用消息: 消息ID:1383095806 来自:48097389945@chatroom 发送人:xiaomaochong 内容:百度上的时间不对 引用类型:1
2025-08-01 10:13:05 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 10:13:05 | INFO | [DouBaoImageToImage] 消息内容: '百度上的时间不对' from xiaomaochong in 48097389945@chatroom
2025-08-01 10:13:05 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['百度上的时间不对']
2025-08-01 10:13:05 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 10:13:05 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 10:13:05 | INFO |   - 消息内容: 百度上的时间不对
2025-08-01 10:13:05 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 10:13:05 | INFO |   - 发送人: xiaomaochong
2025-08-01 10:13:05 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n去百度上搜吧', 'Msgid': '5450739775360089383', 'NewMsgId': '5450739775360089383', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '【微信红包】收到一个微信红包', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_LPZjZ3Je|v1_SZIsaBDl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754014327', 'SenderWxid': 'xiaomaochong'}
2025-08-01 10:13:05 | INFO |   - 引用消息ID: 
2025-08-01 10:13:05 | INFO |   - 引用消息类型: 
2025-08-01 10:13:05 | INFO |   - 引用消息内容: 
去百度上搜吧
2025-08-01 10:13:05 | INFO |   - 引用消息发送人: xiaomaochong
