2025-08-01 09:47:10 | SUCCESS | 读取主设置成功
2025-08-01 09:47:10 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 09:47:10 | INFO | 2025/08/01 09:47:10 GetRedisAddr: 127.0.0.1:6379
2025-08-01 09:47:10 | INFO | 2025/08/01 09:47:10 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 09:47:10 | INFO | 2025/08/01 09:47:10 Server start at :9000
2025-08-01 09:47:11 | SUCCESS | WechatAPI服务已启动
2025-08-01 09:47:11 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 09:47:11 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 09:47:11 | SUCCESS | 登录成功
2025-08-01 09:47:11 | SUCCESS | 已开启自动心跳
2025-08-01 09:47:11 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:47:11 | SUCCESS | 数据库初始化成功
2025-08-01 09:47:11 | SUCCESS | 定时任务已启动
2025-08-01 09:47:11 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 09:47:11 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:47:12 | INFO | 播客API初始化成功
2025-08-01 09:47:12 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:47:12 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:47:12 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 09:47:12 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 09:47:12 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 09:47:12 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 09:47:12 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 09:47:12 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 09:47:12 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 09:47:12 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 09:47:12 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 09:47:12 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 09:47:12 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 09:47:12 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 09:47:12 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 09:47:12 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 09:47:12 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 09:47:12 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 09:47:12 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 09:47:12 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 09:47:12 | DEBUG |   - 启用状态: True
2025-08-01 09:47:12 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 09:47:12 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 09:47:12 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 09:47:12 | DEBUG |   - Cookies配置: 已配置
2025-08-01 09:47:12 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 09:47:12 | DEBUG |   - 自然化响应: True
2025-08-01 09:47:12 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 09:47:12 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-01 09:47:12 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 09:47:12 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 09:47:12 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 09:47:12 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 09:47:12 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 09:47:12 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:47:12 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 09:47:12 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 09:47:12 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 09:47:12 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 09:47:12 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 09:47:12 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 09:47:12 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 09:47:13 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 09:47:13 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 09:47:13 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 09:47:13 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 09:47:14 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 09:47:14 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:47:14 | INFO | [yuanbao] 插件初始化完成
2025-08-01 09:47:14 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 09:47:14 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 09:47:14 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 09:47:14 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 09:47:14 | INFO | 处理堆积消息中
2025-08-01 09:47:14 | SUCCESS | 处理堆积消息完毕
2025-08-01 09:47:14 | SUCCESS | 开始处理消息
2025-08-01 09:47:37 | DEBUG | 收到消息: {'MsgId': 1462184731, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 换不同姿势</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>3039325562699437542</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;684415c9ca831ee2b5db0b9a8f9c34f7_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_9esKwq3j|v1_F1cA5tA5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="7177796a7167666b707a796d6e6a6f77" encryver="0" cdnthumbaeskey="7177796a7167666b707a796d6e6a6f77" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" cdnthumblength="3558" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" length="85864" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" hdlength="1487367" md5="f667f99f7e210556370f35f0560c2296"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754007194</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012862, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>684415c9ca831ee2b5db0b9a8f9c34f7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_LP2WadOj|v1_2Ny1F9ii</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 豆包 换不同姿势', 'NewMsgId': 807070177186366331, 'MsgSeq': 871416339}
2025-08-01 09:47:37 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-01 09:47:37 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:47:37 | INFO | 收到引用消息: 消息ID:1462184731 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:豆包 换不同姿势 引用类型:3
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage] 消息内容: '豆包 换不同姿势' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-01 09:47:37 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['豆包', '换不同姿势']
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage] 识别到图生图引用命令: 豆包
2025-08-01 09:47:37 | DEBUG | [DouBaoImageToImage] 解析到提示词: '换不同姿势'
2025-08-01 09:47:37 | DEBUG | [DouBaoImageToImage] 开始解析提示词、风格和比例...
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage] 引用图片图生图参数:
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage]   - 原始提示词: '换不同姿势'
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage]   - 解析后提示词: '换不同姿势，比例「2:3」'
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage]   - 比例: 832x1248 (2:3)
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage]   - 风格: None
2025-08-01 09:47:37 | DEBUG | [DouBaoImageToImage] 开始检查用户限流...
2025-08-01 09:47:37 | DEBUG | [DouBaoImageToImage] 检查用户限流状态: wxid_ubbh6q832tcs21
2025-08-01 09:47:37 | DEBUG | [DouBaoImageToImage] 当前时间: 1754012857.6864836, 冷却时间: 15秒
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage] 新用户 wxid_ubbh6q832tcs21，允许请求
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage] 开始处理引用的图片...
2025-08-01 09:47:37 | INFO | [DouBaoImageToImage] 被引用消息类型: 3
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 成功下载引用的图片并保存到: temp\doubao_image_to_image\quoted_image_1754012858.jpg
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 开始豆包AI处理流程(无通知)，用户: wxid_ubbh6q832tcs21, 提示词: 换不同姿势，比例「2:3」, 比例: 832x1248
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 步骤1: 验证图片文件...
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 图片验证成功，大小: 83.9KB
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 步骤2: 调用豆包AI接口...
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] ========== 开始豆包AI图生图处理 ==========
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 输入参数:
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage]   - 提示词: '换不同姿势，比例「2:3」'
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage]   - 图片路径: temp\doubao_image_to_image\quoted_image_1754012858.jpg
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage]   - 比例配置: 2:3 (832x1248)
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage]   - 风格值: None
2025-08-01 09:47:38 | DEBUG | [DouBaoImageToImage] Cookie配置检查通过，长度: 2200
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 图片文件验证通过，大小: 83.9KB
2025-08-01 09:47:38 | DEBUG | [DouBaoImageToImage] 创建豆包AI生成器实例...
2025-08-01 09:47:38 | INFO | [DouBaoImageToImage] 开始调用豆包AI处理流程...
2025-08-01 09:47:38 | INFO | [DoubaoImageGenerator] 开始处理图片流程 - 路径: temp\doubao_image_to_image\quoted_image_1754012858.jpg, 提示词: '换不同姿势，比例「2:3」', 风格: None
2025-08-01 09:47:38 | DEBUG | [DoubaoImageGenerator] 创建HTTP客户端成功，超时设置: 300秒
2025-08-01 09:47:38 | INFO | [DoubaoImageGenerator] 步骤1: 上传图片...
2025-08-01 09:47:38 | DEBUG | [DoubaoImageGenerator] 开始上传图片: temp\doubao_image_to_image\quoted_image_1754012858.jpg
2025-08-01 09:47:38 | INFO | [DoubaoImageGenerator] 图片信息 - 大小: 83.9KB, 扩展名: .jpg, CRC32: a0bd4c69
2025-08-01 09:47:38 | DEBUG | [DoubaoImageGenerator] 开始获取上传认证...
2025-08-01 09:47:38 | DEBUG | [DoubaoImageGenerator] 请求上传认证 URL: https://www.doubao.com/alice/resource/prepare_upload
2025-08-01 09:47:38 | DEBUG | [DoubaoImageGenerator] 请求参数: {'version_code': '20800', 'language': 'zh', 'device_platform': 'web', 'aid': '497858', 'real_aid': '497858', 'pkg_type': 'release_version', 'device_id': '7468716989062841895', 'web_id': '7468716986638386703', 'tea_uuid': '7468716986638386703', 'use-olympus-account': '1', 'region': 'CN', 'sys_region': 'CN', 'samantha_web': '1', 'pc_version': '2.24.2'}
2025-08-01 09:47:38 | DEBUG | [DoubaoImageGenerator] 请求数据: {'tenant_id': '5', 'scene_id': '5', 'resource_type': 2}
2025-08-01 09:47:40 | DEBUG | [DoubaoImageGenerator] 上传认证响应状态: 200
2025-08-01 09:47:40 | DEBUG | [DoubaoImageGenerator] 上传认证响应: {'code': 0, 'msg': '', 'data': {'service_id': 'a9rns2rl98', 'upload_path_prefix': 'rc/pc/bot-chat', 'upload_host': 'imagex.bytedanceapi.com', 'upload_auth_token': {'access_key': 'AKTPNGYxYzUzNDFkZTE4NDM0M2I5ZTc2NjVkNTE0Y2IyOTU', 'secret_key': '9LK5uxqt7alKRb/asgwfP0ag0Ud2PTCImI5weWy+pJiLoMy/eeRrvXfeutEiGv0U', 'session_token': 'STS2eyJMVEFjY2Vzc0tleUlkIjoiQUtMVFlUZGhPR0ptWVRNNFl6ZG1OR1JoWVRoaE0yWTJPVFl5TW1SbU0yRmhNREEiLCJBY2Nlc3NLZXlJZCI6IkFLVFBOR1l4WXpVek5ERmtaVEU0TkRNME0ySTVaVGMyTmpWa05URTBZMkl5T1RVIiwiU2lnbmVkU2VjcmV0QWNjZXNzS2V5IjoiQmlkNHBHaEQzc0hYby9LS2VhM05oME9PdnF0MDZ4MDRDNlZLRmE0Zk5JOFFFVUJhMGlSRHFKZGFNRjA1eW9ENi9YRWw1WHo0TlprU0lhNnJqRTEvRDhIemEvSlVTUUk1eTFVenlPTHYwZ2s9IiwiRXhwaXJlZFRpbWUiOjE3NTQwMTY0NjYsIlBvbGljeVN0cmluZyI6IntcIlN0YXRlbWVudFwiOlt7XCJFZmZlY3RcIjpcIkFsbG93XCIsXCJBY3Rpb25cIjpbXCJJbWFnZVg6QXBwbHlJbWFnZVVwbG9hZFwiLFwiSW1hZ2VYOkNvbW1pdEltYWdlVXBsb2FkXCJdLFwiUmVzb3VyY2VcIjpbXCJ0cm46SW1hZ2VYOio6KjpTZXJ2aWNlSWQvYTlybnMycmw5OFwiXX0se1wiRWZmZWN0XCI6XCJBbGxvd1wiLFwiQWN0aW9uXCI6W1wiUFNNXCJdLFwiUmVzb3VyY2VcIjpbXCJmbG93LmFsaWNlLnJlc291cmNlX2NlbnRlclwiXX1dfSIsIlNpZ25hdHVyZSI6IjdmZGMzOTFjYzg2MWY1OGZiNDNkNjU3YzcyZjNkNmZhYzQxY2QzMzJkYTMwN2U1NDZlMzkyODYzNTM1YTkyZGYifQ==', 'expired_time': '2025-08-01T10:47:46+08:00', 'current_time': '2025-08-01T09:47:46+08:00'}}}
2025-08-01 09:47:40 | INFO | [DoubaoImageGenerator] 成功获取上传认证，token长度: 1122
2025-08-01 09:47:40 | DEBUG | [DoubaoImageGenerator] 申请上传 URL: https://imagex.bytedanceapi.com/?Action=ApplyImageUpload&Version=2018-08-01&ServiceId=a9rns2rl98&NeedFallback=true&FileSize=85864&FileExtension=.jpg&s=yy49d6n7o6p
2025-08-01 09:47:40 | DEBUG | [DoubaoImageGenerator] 申请上传请求头: {'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'Accept': '*/*', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Accept-Encoding': 'gzip, deflate', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/create-image', 'X-Requested-With': 'mark.via', 'Authorization': 'AWS4-HMAC-SHA256 Credential=AKTPNGYxYzUzNDFkZTE4NDM0M2I5ZTc2NjVkNTE0Y2IyOTU/20250801/cn-north-1/imagex/aws4_request, SignedHeaders=host;x-amz-date;x-amz-security-token, Signature=c35be33e962ceea033f615dbeb4ccf2e74dcf1dc2e320bd776674e82ddb31b78', 'X-Amz-Date': '20250801T014740Z', 'x-amz-security-token': 'STS2eyJMVEFjY2Vzc0tleUlkIjoiQUtMVFlUZGhPR0ptWVRNNFl6ZG1OR1JoWVRoaE0yWTJPVFl5TW1SbU0yRmhNREEiLCJBY2Nlc3NLZXlJZCI6IkFLVFBOR1l4WXpVek5ERmtaVEU0TkRNME0ySTVaVGMyTmpWa05URTBZMkl5T1RVIiwiU2lnbmVkU2VjcmV0QWNjZXNzS2V5IjoiQmlkNHBHaEQzc0hYby9LS2VhM05oME9PdnF0MDZ4MDRDNlZLRmE0Zk5JOFFFVUJhMGlSRHFKZGFNRjA1eW9ENi9YRWw1WHo0TlprU0lhNnJqRTEvRDhIemEvSlVTUUk1eTFVenlPTHYwZ2s9IiwiRXhwaXJlZFRpbWUiOjE3NTQwMTY0NjYsIlBvbGljeVN0cmluZyI6IntcIlN0YXRlbWVudFwiOlt7XCJFZmZlY3RcIjpcIkFsbG93XCIsXCJBY3Rpb25cIjpbXCJJbWFnZVg6QXBwbHlJbWFnZVVwbG9hZFwiLFwiSW1hZ2VYOkNvbW1pdEltYWdlVXBsb2FkXCJdLFwiUmVzb3VyY2VcIjpbXCJ0cm46SW1hZ2VYOio6KjpTZXJ2aWNlSWQvYTlybnMycmw5OFwiXX0se1wiRWZmZWN0XCI6XCJBbGxvd1wiLFwiQWN0aW9uXCI6W1wiUFNNXCJdLFwiUmVzb3VyY2VcIjpbXCJmbG93LmFsaWNlLnJlc291cmNlX2NlbnRlclwiXX1dfSIsIlNpZ25hdHVyZSI6IjdmZGMzOTFjYzg2MWY1OGZiNDNkNjU3YzcyZjNkNmZhYzQxY2QzMzJkYTMwN2U1NDZlMzkyODYzNTM1YTkyZGYifQ=='}
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 申请上传响应状态: 200
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 申请上传成功: {'UploadAddress': {'StoreInfos': [{'StoreUri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg', 'Auth': 'SpaceKey/a9rns2rl98/1/:version:v2:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xbzLm3m_jRLFW0dgxgCwf9mboCsfMRUtUxkA9FB8vHY', 'UploadID': '79c835e1fd7046b1889f9288e1f3f013'}], 'UploadHosts': ['tos-lf-x.snssdk.com'], 'UploadHeader': None, 'SessionKey': 'eyJhY2NvdW50VHlwZSI6IkltYWdlWCIsImFwcElkIjoiIiwiYml6VHlwZSI6IiIsImZpbGVUeXBlIjoiaW1hZ2UiLCJsZWdhbCI6IiIsInN0b3JlSW5mb3MiOiJbe1wiU3RvcmVVcmlcIjpcInRvcy1jbi1pLWE5cm5zMnJsOTgvNDc4NDgwN2MyNmExNGQ2YmI4NWE5MTgwZDIyODE0ZDkuanBnXCIsXCJBdXRoXCI6XCJTcGFjZUtleS9hOXJuczJybDk4LzEvOnZlcnNpb246djI6ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmxlSEFpT2pFM05UUXdNelEwTmpjc0luTnBaMjVoZEhWeVpVbHVabThpT25zaVlXTmpaWE56UzJWNUlqb2labUZyWlY5aFkyTmxjM05mYTJWNUlpd2lZblZqYTJWMElqb2lkRzl6TFdOdUxXa3RZVGx5Ym5NeWNtdzVPQ0lzSW1WNGNHbHlaU0k2TVRjMU5EQXpORFEyTnl3aVptbHNaVWx1Wm05eklqcGJleUp2YVdSTFpYa2lPaUkwTnpnME9EQTNZekkyWVRFMFpEWmlZamcxWVRreE9EQmtNakk0TVRSa09TNXFjR2NpTENKbWFXeGxWSGx3WlNJNklqRWlmVjBzSW1WNGRISmhJanA3SW1GalkyOTFiblJmY0hKdlpIVmpkQ0k2SW1sdFlXZGxlQ0lzSW1Kc2IyTnJYMjF2WkdVaU9pSWlMQ0pqYjI1MFpXNTBYM1I1Y0dWZllteHZZMnNpT2lKN1hDSnRhVzFsWDNCamRGd2lPakFzWENKdGIyUmxYQ0k2TUN4Y0ltMXBiV1ZmYkdsemRGd2lPbTUxYkd3c1hDSmpiMjVtYkdsamRGOWliRzlqYTF3aU9tWmhiSE5sZlNJc0ltVnVZM0o1Y0hSZllXeG5ieUk2SWlJc0ltVnVZM0o1Y0hSZmEyVjVJam9pSWl3aVpYaDBYMk52Ym5SbGJuUmZkSGx3WlNJNkltbHRZV2RsTDJwd1pXY2lMQ0pwYzE5cGJXRm5aWGdpT25SeWRXVXNJbk53WVdObElqb2lZVGx5Ym5NeWNtdzVPQ0o5ZlgwLnhiekxtM21falJMRlcwZGd4Z0N3ZjltYm9Dc2ZNUlV0VXhrQTlGQjh2SFlcIixcIlVwbG9hZElEXCI6XCI3OWM4MzVlMWZkNzA0NmIxODg5ZjkyODhlMWYzZjAxM1wiLFwiVXBsb2FkSGVhZGVyXCI6bnVsbCxcIlN0b3JhZ2VIZWFkZXJcIjpudWxsfV0iLCJ1cGxvYWRIb3N0IjoidG9zLWxmLXguc25zc2RrLmNvbSIsInVyaSI6InRvcy1jbi1pLWE5cm5zMnJsOTgvNDc4NDgwN2MyNmExNGQ2YmI4NWE5MTgwZDIyODE0ZDkuanBnIiwidXNlcklkIjoiIn0=', 'Cloud': ''}, 'FallbackUploadAddress': {'StoreInfos': None, 'UploadHosts': None, 'UploadHeader': None, 'SessionKey': '', 'Cloud': ''}, 'InnerUploadAddress': {'UploadNodes': [{'StoreInfos': [{'StoreUri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg', 'Auth': 'SpaceKey/a9rns2rl98/1/:version:v2:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xbzLm3m_jRLFW0dgxgCwf9mboCsfMRUtUxkA9FB8vHY', 'UploadID': '79c835e1fd7046b1889f9288e1f3f013'}], 'UploadHost': 'tos-lf-x.snssdk.com', 'UploadHeader': None, 'SessionKey': 'eyJhY2NvdW50VHlwZSI6IkltYWdlWCIsImFwcElkIjoiIiwiYml6VHlwZSI6IiIsImZpbGVUeXBlIjoiaW1hZ2UiLCJsZWdhbCI6IiIsInN0b3JlSW5mb3MiOiJbe1wiU3RvcmVVcmlcIjpcInRvcy1jbi1pLWE5cm5zMnJsOTgvNDc4NDgwN2MyNmExNGQ2YmI4NWE5MTgwZDIyODE0ZDkuanBnXCIsXCJBdXRoXCI6XCJTcGFjZUtleS9hOXJuczJybDk4LzEvOnZlcnNpb246djI6ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmxlSEFpT2pFM05UUXdNelEwTmpjc0luTnBaMjVoZEhWeVpVbHVabThpT25zaVlXTmpaWE56UzJWNUlqb2labUZyWlY5aFkyTmxjM05mYTJWNUlpd2lZblZqYTJWMElqb2lkRzl6TFdOdUxXa3RZVGx5Ym5NeWNtdzVPQ0lzSW1WNGNHbHlaU0k2TVRjMU5EQXpORFEyTnl3aVptbHNaVWx1Wm05eklqcGJleUp2YVdSTFpYa2lPaUkwTnpnME9EQTNZekkyWVRFMFpEWmlZamcxWVRreE9EQmtNakk0TVRSa09TNXFjR2NpTENKbWFXeGxWSGx3WlNJNklqRWlmVjBzSW1WNGRISmhJanA3SW1GalkyOTFiblJmY0hKdlpIVmpkQ0k2SW1sdFlXZGxlQ0lzSW1Kc2IyTnJYMjF2WkdVaU9pSWlMQ0pqYjI1MFpXNTBYM1I1Y0dWZllteHZZMnNpT2lKN1hDSnRhVzFsWDNCamRGd2lPakFzWENKdGIyUmxYQ0k2TUN4Y0ltMXBiV1ZmYkdsemRGd2lPbTUxYkd3c1hDSmpiMjVtYkdsamRGOWliRzlqYTF3aU9tWmhiSE5sZlNJc0ltVnVZM0o1Y0hSZllXeG5ieUk2SWlJc0ltVnVZM0o1Y0hSZmEyVjVJam9pSWl3aVpYaDBYMk52Ym5SbGJuUmZkSGx3WlNJNkltbHRZV2RsTDJwd1pXY2lMQ0pwYzE5cGJXRm5aWGdpT25SeWRXVXNJbk53WVdObElqb2lZVGx5Ym5NeWNtdzVPQ0o5ZlgwLnhiekxtM21falJMRlcwZGd4Z0N3ZjltYm9Dc2ZNUlV0VXhrQTlGQjh2SFlcIixcIlVwbG9hZElEXCI6XCI3OWM4MzVlMWZkNzA0NmIxODg5ZjkyODhlMWYzZjAxM1wiLFwiVXBsb2FkSGVhZGVyXCI6bnVsbCxcIlN0b3JhZ2VIZWFkZXJcIjpudWxsfV0iLCJ1cGxvYWRIb3N0IjoidG9zLWxmLXguc25zc2RrLmNvbSIsInVyaSI6InRvcy1jbi1pLWE5cm5zMnJsOTgvNDc4NDgwN2MyNmExNGQ2YmI4NWE5MTgwZDIyODE0ZDkuanBnIiwidXNlcklkIjoiIn0='}]}, 'RequestId': '202508010947477FB19C6E42287E9059CD', 'SDKParam': None}
2025-08-01 09:47:41 | INFO | [DoubaoImageGenerator] 开始上传到: https://tos-lf-x.snssdk.com/upload/v1/tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 上传请求头: {'Authorization': 'SpaceKey/a9rns2rl98/1/:version:v2:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xbzLm3m_jRLFW0dgxgCwf9mboCsfMRUtUxkA9FB8vHY', 'Content-CRC32': 'a0bd4c69', 'Content-Type': 'application/octet-stream', 'X-Storage-U': '79c835e1fd7046b1889f9288e1f3f013', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 上传响应状态: 200
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 上传响应: {'code': 2000, 'apiversion': 'v1', 'message': 'Success', 'data': {'crc32': 'a0bd4c69'}}
2025-08-01 09:47:41 | INFO | [DoubaoImageGenerator] 图片上传成功
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 提交上传 URL: https://imagex.bytedanceapi.com/?Action=CommitImageUpload&Version=2018-08-01&ServiceId=a9rns2rl98
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 提交载荷: {"SessionKey": "eyJhY2NvdW50VHlwZSI6IkltYWdlWCIsImFwcElkIjoiIiwiYml6VHlwZSI6IiIsImZpbGVUeXBlIjoiaW1hZ2UiLCJsZWdhbCI6IiIsInN0b3JlSW5mb3MiOiJbe1wiU3RvcmVVcmlcIjpcInRvcy1jbi1pLWE5cm5zMnJsOTgvNDc4NDgwN2MyNmExNGQ2YmI4NWE5MTgwZDIyODE0ZDkuanBnXCIsXCJBdXRoXCI6XCJTcGFjZUtleS9hOXJuczJybDk4LzEvOnZlcnNpb246djI6ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmxlSEFpT2pFM05UUXdNelEwTmpjc0luTnBaMjVoZEhWeVpVbHVabThpT25zaVlXTmpaWE56UzJWNUlqb2labUZyWlY5aFkyTmxjM05mYTJWNUlpd2lZblZqYTJWMElqb2lkRzl6TFdOdUxXa3RZVGx5Ym5NeWNtdzVPQ0lzSW1WNGNHbHlaU0k2TVRjMU5EQXpORFEyTnl3aVptbHNaVWx1Wm05eklqcGJleUp2YVdSTFpYa2lPaUkwTnpnME9EQTNZekkyWVRFMFpEWmlZamcxWVRreE9EQmtNakk0TVRSa09TNXFjR2NpTENKbWFXeGxWSGx3WlNJNklqRWlmVjBzSW1WNGRISmhJanA3SW1GalkyOTFiblJmY0hKdlpIVmpkQ0k2SW1sdFlXZGxlQ0lzSW1Kc2IyTnJYMjF2WkdVaU9pSWlMQ0pqYjI1MFpXNTBYM1I1Y0dWZllteHZZMnNpT2lKN1hDSnRhVzFsWDNCamRGd2lPakFzWENKdGIyUmxYQ0k2TUN4Y0ltMXBiV1ZmYkdsemRGd2lPbTUxYkd3c1hDSmpiMjVtYkdsamRGOWliRzlqYTF3aU9tWmhiSE5sZlNJc0ltVnVZM0o1Y0hSZllXeG5ieUk2SWlJc0ltVnVZM0o1Y0hSZmEyVjVJam9pSWl3aVpYaDBYMk52Ym5SbGJuUmZkSGx3WlNJNkltbHRZV2RsTDJwd1pXY2lMQ0pwYzE5cGJXRm5aWGdpT25SeWRXVXNJbk53WVdObElqb2lZVGx5Ym5NeWNtdzVPQ0o5ZlgwLnhiekxtM21falJMRlcwZGd4Z0N3ZjltYm9Dc2ZNUlV0VXhrQTlGQjh2SFlcIixcIlVwbG9hZElEXCI6XCI3OWM4MzVlMWZkNzA0NmIxODg5ZjkyODhlMWYzZjAxM1wiLFwiVXBsb2FkSGVhZGVyXCI6bnVsbCxcIlN0b3JhZ2VIZWFkZXJcIjpudWxsfV0iLCJ1cGxvYWRIb3N0IjoidG9zLWxmLXguc25zc2RrLmNvbSIsInVyaSI6InRvcy1jbi1pLWE5cm5zMnJsOTgvNDc4NDgwN2MyNmExNGQ2YmI4NWE5MTgwZDIyODE0ZDkuanBnIiwidXNlcklkIjoiIn0="}
2025-08-01 09:47:41 | DEBUG | [DoubaoImageGenerator] 提交请求头: {'Content-Type': 'application/json', 'Accept': '*/*', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'Authorization': 'AWS4-HMAC-SHA256 Credential=AKTPNGYxYzUzNDFkZTE4NDM0M2I5ZTc2NjVkNTE0Y2IyOTU/20250801/cn-north-1/imagex/aws4_request, SignedHeaders=host;x-amz-date;x-amz-security-token, Signature=9f9894f934054511c89b8f2636dac3dad8163b619a35a7133c338c0ac96a60a7', 'X-Amz-Date': '20250801T014741Z', 'x-amz-security-token': 'STS2eyJMVEFjY2Vzc0tleUlkIjoiQUtMVFlUZGhPR0ptWVRNNFl6ZG1OR1JoWVRoaE0yWTJPVFl5TW1SbU0yRmhNREEiLCJBY2Nlc3NLZXlJZCI6IkFLVFBOR1l4WXpVek5ERmtaVEU0TkRNME0ySTVaVGMyTmpWa05URTBZMkl5T1RVIiwiU2lnbmVkU2VjcmV0QWNjZXNzS2V5IjoiQmlkNHBHaEQzc0hYby9LS2VhM05oME9PdnF0MDZ4MDRDNlZLRmE0Zk5JOFFFVUJhMGlSRHFKZGFNRjA1eW9ENi9YRWw1WHo0TlprU0lhNnJqRTEvRDhIemEvSlVTUUk1eTFVenlPTHYwZ2s9IiwiRXhwaXJlZFRpbWUiOjE3NTQwMTY0NjYsIlBvbGljeVN0cmluZyI6IntcIlN0YXRlbWVudFwiOlt7XCJFZmZlY3RcIjpcIkFsbG93XCIsXCJBY3Rpb25cIjpbXCJJbWFnZVg6QXBwbHlJbWFnZVVwbG9hZFwiLFwiSW1hZ2VYOkNvbW1pdEltYWdlVXBsb2FkXCJdLFwiUmVzb3VyY2VcIjpbXCJ0cm46SW1hZ2VYOio6KjpTZXJ2aWNlSWQvYTlybnMycmw5OFwiXX0se1wiRWZmZWN0XCI6XCJBbGxvd1wiLFwiQWN0aW9uXCI6W1wiUFNNXCJdLFwiUmVzb3VyY2VcIjpbXCJmbG93LmFsaWNlLnJlc291cmNlX2NlbnRlclwiXX1dfSIsIlNpZ25hdHVyZSI6IjdmZGMzOTFjYzg2MWY1OGZiNDNkNjU3YzcyZjNkNmZhYzQxY2QzMzJkYTMwN2U1NDZlMzkyODYzNTM1YTkyZGYifQ=='}
2025-08-01 09:47:42 | DEBUG | [DoubaoImageGenerator] 提交响应状态: 200
2025-08-01 09:47:42 | DEBUG | [DoubaoImageGenerator] 提交响应: {'ResponseMetadata': {'RequestId': '20250801094747C79BD9D092B39829F657', 'Action': 'CommitImageUpload', 'Version': '2018-08-01', 'Service': 'imagex', 'Region': 'cn-north-1'}, 'Result': {'Results': [{'Uri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg', 'UriStatus': 2000}], 'RequestId': '20250801094747C79BD9D092B39829F657', 'PluginResult': [{'FileName': '4784807c26a14d6bb85a9180d22814d9.jpg', 'SourceUri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg', 'ImageUri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg', 'ImageWidth': 720, 'ImageHeight': 1280, 'ImageMd5': '1ed45ba2db54f109397516aa7637b502', 'ImageFormat': 'jpeg', 'ImageSize': 85864, 'FrameCnt': 1}]}}
2025-08-01 09:47:42 | INFO | [DoubaoImageGenerator] 图片上传完成，URI: tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg
2025-08-01 09:47:42 | INFO | [DoubaoImageGenerator] 图片上传成功，URI: tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg
2025-08-01 09:47:42 | INFO | [DoubaoImageGenerator] 步骤2: 生成图片...
2025-08-01 09:47:42 | INFO | [DoubaoImageGenerator] 开始生成图片 - 提示词: '换不同姿势，比例「2:3」', 图片URI: tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg, 风格: None
2025-08-01 09:47:42 | DEBUG | [DoubaoImageGenerator] 请求数据: {
  "messages": [
    {
      "content": "{\"text\": \"\\u6362\\u4e0d\\u540c\\u59ff\\u52bf\\uff0c\\u6bd4\\u4f8b\\u300c2:3\\u300d\\uff01\"}",
      "content_type": 2009,
      "attachments": [
        {
          "type": "image",
          "key": "tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg",
          "extra": {
            "refer_types": "overall"
          },
          "identifier": "247a3a0f-0120-49f0-86fb-16c9c1a82844"
        }
      ],
      "references": []
    }
  ],
  "completion_option": {
    "is_regen": false,
    "with_suggest": false,
    "need_create_conversation": true,
    "launch_stage": 1,
    "is_replace": false,
    "is_delete": false,
    "message_from": 0,
    "use_auto_cot": false,
    "resend_for_regen": false,
    "event_id": "0"
  },
  "conversation_id": "0",
  "local_conversation_id": "local_1754012862058810",
  "local_message_id": "ab788083-6ca0-483e-abbd-8b1f37cc3238"
}
2025-08-01 09:47:42 | DEBUG | [DoubaoImageGenerator] 请求头: {'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Accept-Encoding': 'gzip, deflate', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/create-image', 'X-Requested-With': 'mark.via', 'Content-Type': 'application/json', 'x-flow-trace': '04-c5a2562105f4485e-b193f6e31cf5453e-01', 'Agw-Js-Conv': 'str', 'last-event-id': 'undefined', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty'}
2025-08-01 09:47:42 | DEBUG | [DoubaoImageGenerator] 请求URL: https://www.doubao.com/samantha/chat/completion?version_code=20800&language=zh&device_platform=web&aid=497858&real_aid=497858&pkg_type=release_version&device_id=7468716989062841895&web_id=7468716986638386703&tea_uuid=7468716986638386703&use-olympus-account=1&region=CN&sys_region=CN&samantha_web=1&pc_version=2.24.2
2025-08-01 09:47:43 | DEBUG | [DoubaoImageGenerator] 流式响应状态: 200
2025-08-01 09:47:43 | INFO | [DoubaoImageGenerator] 开始处理流式响应...
2025-08-01 09:47:43 | DEBUG | [DoubaoImageGenerator] 第1行事件数据: {'event_data': '{"message_id":"*****************","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","message_index":1,"conversation_type":5}', 'event_id': '0', 'event_type': 2002}
2025-08-01 09:47:43 | DEBUG | [DoubaoImageGenerator] 第3行事件数据: {'event_data': '{"proto_version":2,"message_id":"*****************","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","message_index":1,"conversation_type":5}', 'event_id': '1', 'event_type': 2011}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第5行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"好\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '2', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"好"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第7行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '3', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第9行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '4', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第11行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"我\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '5', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"我"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第13行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"将\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '6', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"将"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第15行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '7', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第17行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"你\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '8', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"你"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第19行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"上传\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '9', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"上传"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第21行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '10', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第23行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"图片\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '11', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"图片"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第25行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"中的\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '12', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"中的"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第27行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"人物\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '13', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"人物"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第29行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"更换\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '14', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"更换"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第31行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"不同\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '15', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"不同"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第33行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '16', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第35行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，并\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '17', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，并"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 第37行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"将\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '18', 'event_type': 2001}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"将"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:47 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 第39行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"比例\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '19', 'event_type': 2001}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"比例"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 第41行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"调整\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '20', 'event_type': 2001}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"调整"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 第43行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '21', 'event_type': 2001}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 第45行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"2\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '22', 'event_type': 2001}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"2"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 第47行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\":\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '23', 'event_type': 2001}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":":"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 第49行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"3\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '24', 'event_type': 2001}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"3"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 第51行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '25', 'event_type': 2001}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': 'e8bf16b1-1b8d-4eb6-b6e2-c2145515dcb2'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。
2025-08-01 09:47:48 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第53行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"26f684bf-4ad1-420d-9208-b274928e47d9"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '26', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': '26f684bf-4ad1-420d-9208-b274928e47d9'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 09:47:51 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 09:47:51 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第55行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"已\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '27', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"已"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第57行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"完成\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '28', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"完成"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第59行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"一种\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '29', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"一种"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第61行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '30', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第63行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '31', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第65行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"调整\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '32', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"调整"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第67行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '33', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第69行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"接下来\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '34', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"接下来"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第71行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"继续\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '35', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"继续"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第73行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '36', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第75行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"你\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '37', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"你"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第77行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"生成\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '38', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"生成"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第79行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"其他\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '39', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"其他"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第81行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"不同\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '40', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"不同"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第83行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '41', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 第85行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"fa5f4334-8c32-459b-b2b5-fa276edd432b"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '42', 'event_type': 2001}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': 'fa5f4334-8c32-459b-b2b5-fa276edd432b'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。
2025-08-01 09:47:51 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第87行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"cf66f6f4-96cc-44c9-9987-fc7fa75002b6"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '43', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': 'cf66f6f4-96cc-44c9-9987-fc7fa75002b6'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 09:47:54 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 09:47:54 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第89行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"又\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '44', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"又"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第91行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"完成\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '45', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"完成"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第93行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"一种\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '46', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"一种"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第95行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '47', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第97行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '48', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第99行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"调整\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '49', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"调整"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第101行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '50', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第103行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"下面\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '51', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"下面"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 第105行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"生成\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '52', 'event_type': 2001}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"生成"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成
2025-08-01 09:47:54 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 第107行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"最后\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '53', 'event_type': 2001}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"最后"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 第109行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"一种\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '54', 'event_type': 2001}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"一种"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 第111行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"不同\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '55', 'event_type': 2001}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"不同"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 第113行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '56', 'event_type': 2001}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 第115行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"b3ff68b7-3710-439d-a9f2-da777b4a03a4"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '57', 'event_type': 2001}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': 'b3ff68b7-3710-439d-a9f2-da777b4a03a4'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。
2025-08-01 09:47:55 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。
2025-08-01 09:47:58 | DEBUG | [DoubaoImageGenerator] 第117行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"3f75e06c-e968-468a-a943-73cd59001a54"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '58', 'event_type': 2001}
2025-08-01 09:47:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': '3f75e06c-e968-468a-a943-73cd59001a54'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:47:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:47:58 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 09:47:58 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:47:58 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 09:47:58 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 09:48:06 | DEBUG | [DoubaoImageGenerator] 第119行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"key\\":\\"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db\\",\\"image_thumb\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=XhLQ5uYqSTYue2LJOy6gPy4KHJ0%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"image_ori\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=JH3IbryGRA%2BtcyFePEiwO04R9FU%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_raw\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_thumb_ori\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=2Kpnybmhi4xdeDh592b8M1jLylg%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"description\\":\\"图片\\",\\"image_thumb_formats\\":{\\"avif\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=FMC7HfNRMrttfCcQvEw55wU2OaM%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=8VTtse5thozxc5TgKZBO%2Fehopr4%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"image_thumb_ori_formats\\":{\\"avif\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=XdkFQP8f3%2F3L2WWR51%2FsBTm6n0E%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=djBUjuJAwYtHW5P0J%2FW7wnH7E9k%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"status\\":2,\\"gen_params\\":{\\"prompt\\":\\"人物姿势调整为侧身站立，左手自然下垂，右手轻扶楼梯扶手，头部微微向左侧倾斜\\",\\"neg_prompt\\":\\"画面模糊，低质量\\",\\"img_uri\\":\\"tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg\\"},\\"preview_img\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372889\\\\u0026x-signature=oo7paHRbkpp5lLL1UTr0yAuIyVA%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536}}}]}","reset":true,"id":"26f684bf-4ad1-420d-9208-b274928e47d9"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '59', 'event_type': 2001}
2025-08-01 09:48:06 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"key":"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db","image_thumb":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=XhLQ5uYqSTYue2LJOy6gPy4KHJ0%3D","format":"jpeg","width":386,"height":580},"image_ori":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=JH3IbryGRA%2BtcyFePEiwO04R9FU%3D","format":"png","width":1024,"height":1536},"image_raw":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D","format":"png","width":1024,"height":1536},"image_thumb_ori":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=2Kpnybmhi4xdeDh592b8M1jLylg%3D","format":"jpeg","width":386,"height":580},"description":"图片","image_thumb_formats":{"avif":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=FMC7HfNRMrttfCcQvEw55wU2OaM%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=8VTtse5thozxc5TgKZBO%2Fehopr4%3D","format":"webp","width":386,"height":580}},"image_thumb_ori_formats":{"avif":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=XdkFQP8f3%2F3L2WWR51%2FsBTm6n0E%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=djBUjuJAwYtHW5P0J%2FW7wnH7E9k%3D","format":"webp","width":386,"height":580}},"status":2,"gen_params":{"prompt":"人物姿势调整为侧身站立，左手自然下垂，右手轻扶楼梯扶手，头部微微向左侧倾斜","neg_prompt":"画面模糊，低质量","img_uri":"tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg"},"preview_img":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372889\\u0026x-signature=oo7paHRbkpp5lLL1UTr0yAuIyVA%3D","format":"png","width":1024,"height":1536}}}]}', 'reset': True, 'id': '26f684bf-4ad1-420d-9208-b274928e47d9'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:48:06 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:48:06 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db', 'image_thumb': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=XhLQ5uYqSTYue2LJOy6gPy4KHJ0%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=JH3IbryGRA%2BtcyFePEiwO04R9FU%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=2Kpnybmhi4xdeDh592b8M1jLylg%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=FMC7HfNRMrttfCcQvEw55wU2OaM%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=8VTtse5thozxc5TgKZBO%2Fehopr4%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=XdkFQP8f3%2F3L2WWR51%2FsBTm6n0E%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=djBUjuJAwYtHW5P0J%2FW7wnH7E9k%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为侧身站立，左手自然下垂，右手轻扶楼梯扶手，头部微微向左侧倾斜', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg'}, 'preview_img': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=oo7paHRbkpp5lLL1UTr0yAuIyVA%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}]}
2025-08-01 09:48:06 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:48:06 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db', 'image_thumb': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=XhLQ5uYqSTYue2LJOy6gPy4KHJ0%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=JH3IbryGRA%2BtcyFePEiwO04R9FU%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=2Kpnybmhi4xdeDh592b8M1jLylg%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=FMC7HfNRMrttfCcQvEw55wU2OaM%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=8VTtse5thozxc5TgKZBO%2Fehopr4%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=XdkFQP8f3%2F3L2WWR51%2FsBTm6n0E%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=djBUjuJAwYtHW5P0J%2FW7wnH7E9k%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为侧身站立，左手自然下垂，右手轻扶楼梯扶手，头部微微向左侧倾斜', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg'}, 'preview_img': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=oo7paHRbkpp5lLL1UTr0yAuIyVA%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}
2025-08-01 09:48:06 | INFO | [DoubaoImageGenerator] 图片状态: 2
2025-08-01 09:48:06 | INFO | [DoubaoImageGenerator] 图片生成成功，URL类型: image_raw, URL: https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D
2025-08-01 09:48:06 | INFO | [DoubaoImageGenerator] 创建配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续...', 图片=https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D
2025-08-01 09:48:09 | DEBUG | [DoubaoImageGenerator] 第121行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"key\\":\\"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3\\",\\"image_thumb\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=qT3UNXJeM3ptH29G9nIn3AMQ4CQ%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"image_ori\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=cn0AlLKAVAK4aEfyIrTiwli%2BcJg%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_raw\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_thumb_ori\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=fB0JTEGy2%2FC605IAilFPfmW4Ay4%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"description\\":\\"图片\\",\\"image_thumb_formats\\":{\\"avif\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=gcq6n5PIhiORWt5izoiK2fttYpg%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=PVkY4ZKWJSyTYcESTztwkbyY2mk%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"image_thumb_ori_formats\\":{\\"avif\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=M7NcB1IcRvKgJZbNkopjH6Y%2Buhw%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=tXN%2BnbJSMfgypPU4ZXjic%2BPlnwg%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"status\\":2,\\"gen_params\\":{\\"prompt\\":\\"人物正面对镜头，双脚并拢，双手交叠放在身前手提包上，身体微微前倾\\",\\"neg_prompt\\":\\"画面模糊，低质量\\",\\"img_uri\\":\\"tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg\\"},\\"preview_img\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372893\\\\u0026x-signature=UquJH2IIzZ1Ps%2Fp0LJr7FnVemdg%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536}}}]}","reset":true,"id":"cf66f6f4-96cc-44c9-9987-fc7fa75002b6"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '60', 'event_type': 2001}
2025-08-01 09:48:09 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"key":"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3","image_thumb":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=qT3UNXJeM3ptH29G9nIn3AMQ4CQ%3D","format":"jpeg","width":386,"height":580},"image_ori":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=cn0AlLKAVAK4aEfyIrTiwli%2BcJg%3D","format":"png","width":1024,"height":1536},"image_raw":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D","format":"png","width":1024,"height":1536},"image_thumb_ori":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=fB0JTEGy2%2FC605IAilFPfmW4Ay4%3D","format":"jpeg","width":386,"height":580},"description":"图片","image_thumb_formats":{"avif":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=gcq6n5PIhiORWt5izoiK2fttYpg%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=PVkY4ZKWJSyTYcESTztwkbyY2mk%3D","format":"webp","width":386,"height":580}},"image_thumb_ori_formats":{"avif":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=M7NcB1IcRvKgJZbNkopjH6Y%2Buhw%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=tXN%2BnbJSMfgypPU4ZXjic%2BPlnwg%3D","format":"webp","width":386,"height":580}},"status":2,"gen_params":{"prompt":"人物正面对镜头，双脚并拢，双手交叠放在身前手提包上，身体微微前倾","neg_prompt":"画面模糊，低质量","img_uri":"tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg"},"preview_img":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372893\\u0026x-signature=UquJH2IIzZ1Ps%2Fp0LJr7FnVemdg%3D","format":"png","width":1024,"height":1536}}}]}', 'reset': True, 'id': 'cf66f6f4-96cc-44c9-9987-fc7fa75002b6'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:48:09 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:48:09 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3', 'image_thumb': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=qT3UNXJeM3ptH29G9nIn3AMQ4CQ%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=cn0AlLKAVAK4aEfyIrTiwli%2BcJg%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=fB0JTEGy2%2FC605IAilFPfmW4Ay4%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=gcq6n5PIhiORWt5izoiK2fttYpg%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=PVkY4ZKWJSyTYcESTztwkbyY2mk%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=M7NcB1IcRvKgJZbNkopjH6Y%2Buhw%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=tXN%2BnbJSMfgypPU4ZXjic%2BPlnwg%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物正面对镜头，双脚并拢，双手交叠放在身前手提包上，身体微微前倾', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg'}, 'preview_img': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=UquJH2IIzZ1Ps%2Fp0LJr7FnVemdg%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}]}
2025-08-01 09:48:09 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:48:09 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3', 'image_thumb': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=qT3UNXJeM3ptH29G9nIn3AMQ4CQ%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=cn0AlLKAVAK4aEfyIrTiwli%2BcJg%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=fB0JTEGy2%2FC605IAilFPfmW4Ay4%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=gcq6n5PIhiORWt5izoiK2fttYpg%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=PVkY4ZKWJSyTYcESTztwkbyY2mk%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=M7NcB1IcRvKgJZbNkopjH6Y%2Buhw%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=tXN%2BnbJSMfgypPU4ZXjic%2BPlnwg%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物正面对镜头，双脚并拢，双手交叠放在身前手提包上，身体微微前倾', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg'}, 'preview_img': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=UquJH2IIzZ1Ps%2Fp0LJr7FnVemdg%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}
2025-08-01 09:48:09 | INFO | [DoubaoImageGenerator] 图片状态: 2
2025-08-01 09:48:09 | INFO | [DoubaoImageGenerator] 图片生成成功，URL类型: image_raw, URL: https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D
2025-08-01 09:48:09 | INFO | [DoubaoImageGenerator] 创建配对 2: 文本='...', 图片=https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 第123行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"key\\":\\"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e\\",\\"image_thumb\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=aqIH4%2FJhlPvHfPce2qi1zeXe7Ts%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"image_ori\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=pyCXf0gGIdoaZRXlKb0aHH59B1w%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_raw\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_thumb_ori\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=BMQlNzdxjwYCq862Iwxdnwx3JHg%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"description\\":\\"图片\\",\\"image_thumb_formats\\":{\\"avif\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=IjmyCRY7P67Ugg6CMRaIxgbE3kI%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=k9hpyjkH1zKzaug9MrWFdLV3bHw%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"image_thumb_ori_formats\\":{\\"avif\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=9SSLbKjlNG8eGxaZ09m8DT%2FoJlU%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=IFYCe6qNDbor7RgVtj4WEfGXXwk%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"status\\":2,\\"gen_params\\":{\\"prompt\\":\\"人物姿势调整为单脚在前交叉站立，右手拿着手提包自然垂于身侧，左手轻撩头发，头部转向右侧\\",\\"neg_prompt\\":\\"画面模糊，低质量\\",\\"img_uri\\":\\"tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg\\"},\\"preview_img\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069372898\\\\u0026x-signature=GYBTx%2FkoYURCFAGFwLNzwNBuAdY%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536}}}]}","reset":true,"id":"3f75e06c-e968-468a-a943-73cd59001a54"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '61', 'event_type': 2001}
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"key":"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e","image_thumb":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=aqIH4%2FJhlPvHfPce2qi1zeXe7Ts%3D","format":"jpeg","width":386,"height":580},"image_ori":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=pyCXf0gGIdoaZRXlKb0aHH59B1w%3D","format":"png","width":1024,"height":1536},"image_raw":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D","format":"png","width":1024,"height":1536},"image_thumb_ori":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=BMQlNzdxjwYCq862Iwxdnwx3JHg%3D","format":"jpeg","width":386,"height":580},"description":"图片","image_thumb_formats":{"avif":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=IjmyCRY7P67Ugg6CMRaIxgbE3kI%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=k9hpyjkH1zKzaug9MrWFdLV3bHw%3D","format":"webp","width":386,"height":580}},"image_thumb_ori_formats":{"avif":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=9SSLbKjlNG8eGxaZ09m8DT%2FoJlU%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=IFYCe6qNDbor7RgVtj4WEfGXXwk%3D","format":"webp","width":386,"height":580}},"status":2,"gen_params":{"prompt":"人物姿势调整为单脚在前交叉站立，右手拿着手提包自然垂于身侧，左手轻撩头发，头部转向右侧","neg_prompt":"画面模糊，低质量","img_uri":"tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg"},"preview_img":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069372898\\u0026x-signature=GYBTx%2FkoYURCFAGFwLNzwNBuAdY%3D","format":"png","width":1024,"height":1536}}}]}', 'reset': True, 'id': '3f75e06c-e968-468a-a943-73cd59001a54'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e', 'image_thumb': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=aqIH4%2FJhlPvHfPce2qi1zeXe7Ts%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=pyCXf0gGIdoaZRXlKb0aHH59B1w%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=BMQlNzdxjwYCq862Iwxdnwx3JHg%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=IjmyCRY7P67Ugg6CMRaIxgbE3kI%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=k9hpyjkH1zKzaug9MrWFdLV3bHw%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=9SSLbKjlNG8eGxaZ09m8DT%2FoJlU%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=IFYCe6qNDbor7RgVtj4WEfGXXwk%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为单脚在前交叉站立，右手拿着手提包自然垂于身侧，左手轻撩头发，头部转向右侧', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg'}, 'preview_img': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=GYBTx%2FkoYURCFAGFwLNzwNBuAdY%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}]}
2025-08-01 09:48:14 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e', 'image_thumb': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=aqIH4%2FJhlPvHfPce2qi1zeXe7Ts%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=pyCXf0gGIdoaZRXlKb0aHH59B1w%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=BMQlNzdxjwYCq862Iwxdnwx3JHg%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=IjmyCRY7P67Ugg6CMRaIxgbE3kI%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=k9hpyjkH1zKzaug9MrWFdLV3bHw%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=9SSLbKjlNG8eGxaZ09m8DT%2FoJlU%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=IFYCe6qNDbor7RgVtj4WEfGXXwk%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为单脚在前交叉站立，右手拿着手提包自然垂于身侧，左手轻撩头发，头部转向右侧', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/4784807c26a14d6bb85a9180d22814d9.jpg'}, 'preview_img': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=GYBTx%2FkoYURCFAGFwLNzwNBuAdY%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}
2025-08-01 09:48:14 | INFO | [DoubaoImageGenerator] 图片状态: 2
2025-08-01 09:48:14 | INFO | [DoubaoImageGenerator] 图片生成成功，URL类型: image_raw, URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D
2025-08-01 09:48:14 | INFO | [DoubaoImageGenerator] 创建配对 3: 文本='...', 图片=https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 第125行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{}","id":"7376979e-83e7-485d-ae53-872508548e53"},"message_id":"14451449827667458","local_message_id":"ab788083-6ca0-483e-abbd-8b1f37cc3238","conversation_id":"*****************","local_conversation_id":"local_1754012862058810","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":1,"is_finish":true,"message_action_bar":{},"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781","tts_content":"好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。"}', 'event_id': '62', 'event_type': 2001}
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{}', 'id': '7376979e-83e7-485d-ae53-872508548e53'}, 'message_id': '14451449827667458', 'local_message_id': 'ab788083-6ca0-483e-abbd-8b1f37cc3238', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754012862058810', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 1, 'is_finish': True, 'message_action_bar': {}, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781', 'tts_content': '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。'}
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] TTS文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。
2025-08-01 09:48:14 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。
2025-08-01 09:48:16 | DEBUG | [DoubaoImageGenerator] 第127行事件数据: {'event_data': '{}', 'event_id': '63', 'event_type': 2003}
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 处理剩余文本段落: '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。'
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 文本已配对，跳过重复创建
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 流式响应处理完成，共处理 128 行，耗时 33.1秒
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 总共收集到 3 个文本-图片配对
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续...', 图片=有
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 配对 2: 文本='...', 图片=有
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 配对 3: 文本='...', 图片=有
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 图片处理流程完成，生成了 3 个文本-图片配对
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续...', 图片=有
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 配对 2: 文本='...', 图片=有
2025-08-01 09:48:16 | INFO | [DoubaoImageGenerator] 配对 3: 文本='...', 图片=有
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] 豆包AI处理完成，耗时: 38.2秒
2025-08-01 09:48:16 | DEBUG | [DouBaoImageToImage] 标记临时文件清理: temp\doubao_image_to_image\quoted_image_1754012858.jpg
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] ✅ 豆包AI处理成功，生成了 3 个文本-图片配对
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] 完整文本响应: '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。'
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] ========== 豆包AI图生图处理完成 ==========
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] 豆包AI处理完成，生成了3个文本-图片配对
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] 有效配对数量: 3/3
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] 处理配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续...', 图片=有
2025-08-01 09:48:16 | INFO | [DouBaoImageToImage] 发送文本 1: '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。'
2025-08-01 09:48:16 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。已完成一种姿势的调整，接下来继续为你生成其他不同姿势。又完成一种姿势的调整，下面生成最后一种不同姿势。
2025-08-01 09:48:17 | INFO | [DouBaoImageToImage] 发送图片 1: https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D
2025-08-01 09:48:17 | INFO | [DouBaoImageToImage] ========== 开始发送生成结果 ==========
2025-08-01 09:48:17 | INFO | [DouBaoImageToImage] 待发送图片数量: 1
2025-08-01 09:48:17 | INFO | [DouBaoImageToImage] 处理第 1/1 张图片...
2025-08-01 09:48:17 | DEBUG | [DouBaoImageToImage] 图片URL: https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/83b5c42e3b944108a704f8f5fdce59db~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372889&x-signature=4NT1DDUX3q9NW1Yo6Rjd30Ada3g%3D
2025-08-01 09:48:17 | DEBUG | [DouBaoImageToImage] 开始下载第 1 张图片...
2025-08-01 09:48:20 | INFO | [DouBaoImageToImage] 第 1 张图片下载完成，耗时: 3.5秒
2025-08-01 09:48:20 | INFO | [DouBaoImageToImage] 图片数据验证通过，大小: 1828.4KB
2025-08-01 09:48:20 | DEBUG | [DouBaoImageToImage] 开始发送第 1 张图片...
2025-08-01 09:48:26 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_to_image\quoted_image_1754012858.jpg
2025-08-01 09:48:29 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-08-01 09:48:29 | DEBUG | [DouBaoImageToImage] 发送结果: ('wxid_4usgcju5ey9q29_1754012900', 1754012915, 197497865017085467)
2025-08-01 09:48:29 | INFO | [DouBaoImageToImage] ✅ 第 1/1 张图片发送成功，耗时: 9.1秒
2025-08-01 09:48:29 | DEBUG | [DouBaoImageToImage] 等待1.5秒后处理下一张图片...
2025-08-01 09:48:31 | INFO | [DouBaoImageToImage] ========== 图片发送完成 ==========
2025-08-01 09:48:31 | INFO | [DouBaoImageToImage] 发送结果: 成功 1/1 张
2025-08-01 09:48:31 | INFO | [DouBaoImageToImage] 处理配对 2: 文本='...', 图片=有
2025-08-01 09:48:31 | INFO | [DouBaoImageToImage] 发送图片 2: https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D
2025-08-01 09:48:31 | INFO | [DouBaoImageToImage] ========== 开始发送生成结果 ==========
2025-08-01 09:48:31 | INFO | [DouBaoImageToImage] 待发送图片数量: 1
2025-08-01 09:48:31 | INFO | [DouBaoImageToImage] 处理第 1/1 张图片...
2025-08-01 09:48:31 | DEBUG | [DouBaoImageToImage] 图片URL: https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/8827075b749a4a7796d62e57d302cbe3~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372893&x-signature=a7RszY8zff%2F3kAJxeHufyz5G%2B4w%3D
2025-08-01 09:48:31 | DEBUG | [DouBaoImageToImage] 开始下载第 1 张图片...
2025-08-01 09:48:35 | INFO | [DouBaoImageToImage] 第 1 张图片下载完成，耗时: 3.9秒
2025-08-01 09:48:35 | INFO | [DouBaoImageToImage] 图片数据验证通过，大小: 1861.2KB
2025-08-01 09:48:35 | DEBUG | [DouBaoImageToImage] 开始发送第 1 张图片...
2025-08-01 09:48:44 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-08-01 09:48:44 | DEBUG | [DouBaoImageToImage] 发送结果: ('wxid_4usgcju5ey9q29_1754012915', 1754012930, 5447872295597589625)
2025-08-01 09:48:44 | INFO | [DouBaoImageToImage] ✅ 第 1/1 张图片发送成功，耗时: 9.2秒
2025-08-01 09:48:44 | DEBUG | [DouBaoImageToImage] 等待1.5秒后处理下一张图片...
2025-08-01 09:48:46 | INFO | [DouBaoImageToImage] ========== 图片发送完成 ==========
2025-08-01 09:48:46 | INFO | [DouBaoImageToImage] 发送结果: 成功 1/1 张
2025-08-01 09:48:46 | INFO | [DouBaoImageToImage] 处理配对 3: 文本='...', 图片=有
2025-08-01 09:48:46 | INFO | [DouBaoImageToImage] 发送图片 3: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D
2025-08-01 09:48:46 | INFO | [DouBaoImageToImage] ========== 开始发送生成结果 ==========
2025-08-01 09:48:46 | INFO | [DouBaoImageToImage] 待发送图片数量: 1
2025-08-01 09:48:46 | INFO | [DouBaoImageToImage] 处理第 1/1 张图片...
2025-08-01 09:48:46 | DEBUG | [DouBaoImageToImage] 图片URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/ac0f135833fe4fe2b6f6e2a8f3dacf0e~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069372898&x-signature=PiFYHIsrSQ7cuu25AOLkowzB%2BwM%3D
2025-08-01 09:48:46 | DEBUG | [DouBaoImageToImage] 开始下载第 1 张图片...
2025-08-01 09:48:50 | INFO | [DouBaoImageToImage] 第 1 张图片下载完成，耗时: 3.4秒
2025-08-01 09:48:50 | INFO | [DouBaoImageToImage] 图片数据验证通过，大小: 1888.3KB
2025-08-01 09:48:50 | DEBUG | [DouBaoImageToImage] 开始发送第 1 张图片...
2025-08-01 09:48:59 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-08-01 09:48:59 | DEBUG | [DouBaoImageToImage] 发送结果: ('wxid_4usgcju5ey9q29_1754012930', 1754012944, 5224111692472972855)
2025-08-01 09:48:59 | INFO | [DouBaoImageToImage] ✅ 第 1/1 张图片发送成功，耗时: 8.8秒
2025-08-01 09:48:59 | DEBUG | [DouBaoImageToImage] 等待1.5秒后处理下一张图片...
2025-08-01 09:49:00 | INFO | [DouBaoImageToImage] ========== 图片发送完成 ==========
2025-08-01 09:49:00 | INFO | [DouBaoImageToImage] 发送结果: 成功 1/1 张
2025-08-01 09:49:01 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:49:01 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:49:01 | INFO |   - 消息内容: 豆包 换不同姿势
2025-08-01 09:49:01 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-01 09:49:01 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-01 09:49:01 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 换不同姿势</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>3039325562699437542</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;684415c9ca831ee2b5db0b9a8f9c34f7_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_9esKwq3j|v1_F1cA5tA5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="7177796a7167666b707a796d6e6a6f77" encryver="0" cdnthumbaeskey="7177796a7167666b707a796d6e6a6f77" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" cdnthumblength="3558" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" length="85864" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" hdlength="1487367" md5="f667f99f7e210556370f35f0560c2296"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754007194</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '3039325562699437542', 'NewMsgId': '3039325562699437542', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>684415c9ca831ee2b5db0b9a8f9c34f7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_9esKwq3j|v1_F1cA5tA5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754007194', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-01 09:49:01 | INFO |   - 引用消息ID: 
2025-08-01 09:49:01 | INFO |   - 引用消息类型: 
2025-08-01 09:49:01 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>豆包 换不同姿势</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>3039325562699437542</svrid>
			<fromusr>55878994168@chatroom</fromusr>
			<chatusr>wxid_4usgcju5ey9q29</chatusr>
			<displayname>瑶瑶</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;684415c9ca831ee2b5db0b9a8f9c34f7_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;3&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_9esKwq3j|v1_F1cA5tA5&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="7177796a7167666b707a796d6e6a6f77" encryver="0" cdnthumbaeskey="7177796a7167666b707a796d6e6a6f77" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" cdnthumblength="3558" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" length="85864" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" hdlength="1487367" md5="f667f99f7e210556370f35f0560c2296"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1754007194</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 09:49:01 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-01 09:49:50 | DEBUG | 收到消息: {'MsgId': 327934981, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="27700abbe58b82df0a127e74e97186bc" encryver="1" cdnthumbaeskey="27700abbe58b82df0a127e74e97186bc" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1d43042433366563323661372d316166342d343436642d613963302d346564323763323436313761020405250a020201000405004c4d9900" cdnthumblength="4800" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1d43042433366563323661372d316166342d343436642d613963302d346564323763323436313761020405250a020201000405004c4d9900" length="1028559" md5="d58b7dd5e877c2998abc1d7419fb0e96" hevc_mid_size="91482" originsourcemd5="5763377266c3f67a7154aa47c149ec7d">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImQxMDA1MTAwNTAwMDUwMDAiLCJwZHFIYXNoIjoiY2FiMDE2OTRkNjM0YWM5OWFk\nMGIyMTRiYWQ2YmQ0NWZkNjk2Y2UzYzJjMmJhOTJkZDA5ZTU1NjlmMDFmMzI5NiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012995, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>8e3d2415d01216b0bb509d4fd9c5e832_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_VzCNyTj3|v1_/ceZWxGA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4812222137256807839, 'MsgSeq': 871416348}
2025-08-01 09:49:50 | INFO | 收到图片消息: 消息ID:327934981 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="27700abbe58b82df0a127e74e97186bc" encryver="1" cdnthumbaeskey="27700abbe58b82df0a127e74e97186bc" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1d43042433366563323661372d316166342d343436642d613963302d346564323763323436313761020405250a020201000405004c4d9900" cdnthumblength="4800" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1d43042433366563323661372d316166342d343436642d613963302d346564323763323436313761020405250a020201000405004c4d9900" length="1028559" md5="d58b7dd5e877c2998abc1d7419fb0e96" hevc_mid_size="91482" originsourcemd5="5763377266c3f67a7154aa47c149ec7d"><secHashInfoBase64>eyJwaGFzaCI6ImQxMDA1MTAwNTAwMDUwMDAiLCJwZHFIYXNoIjoiY2FiMDE2OTRkNjM0YWM5OWFkMGIyMTRiYWQ2YmQ0NWZkNjk2Y2UzYzJjMmJhOTJkZDA5ZTU1NjlmMDFmMzI5NiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:49:50 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:49:50 | INFO | [TimerTask] 缓存图片消息: 327934981
2025-08-01 09:49:53 | DEBUG | 收到消息: {'MsgId': 1263355347, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n这么贵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012999, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_NVUp/zgw|v1_e0xL7i/T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8119369105786688173, 'MsgSeq': 871416349}
2025-08-01 09:49:53 | INFO | 收到文本消息: 消息ID:1263355347 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:这么贵
2025-08-01 09:49:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这么贵' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-01 09:49:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['这么贵']
2025-08-01 09:49:53 | DEBUG | 处理消息内容: '这么贵'
2025-08-01 09:49:53 | DEBUG | 消息内容 '这么贵' 不匹配任何命令，忽略
2025-08-01 09:50:26 | DEBUG | 收到消息: {'MsgId': 60890223, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_j2kp7us99uv222:\n这个背饰是能上天吗 300个换'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013031, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_ymcWczuA|v1_Z48WxxZu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8855398848028283109, 'MsgSeq': 871416350}
2025-08-01 09:50:26 | INFO | 收到文本消息: 消息ID:60890223 来自:27852221909@chatroom 发送人:wxid_j2kp7us99uv222 @:[] 内容:这个背饰是能上天吗 300个换
2025-08-01 09:50:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这个背饰是能上天吗 300个换' from wxid_j2kp7us99uv222 in 27852221909@chatroom
2025-08-01 09:50:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['这个背饰是能上天吗', '300个换']
2025-08-01 09:50:26 | DEBUG | 处理消息内容: '这个背饰是能上天吗 300个换'
2025-08-01 09:50:26 | DEBUG | 消息内容 '这个背饰是能上天吗 300个换' 不匹配任何命令，忽略
2025-08-01 09:50:39 | DEBUG | 收到消息: {'MsgId': 1438356712, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qq631390473:\n@枂菟ིྀ\u2005哪来的？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013044, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_5kipwrzramxr22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_tR+utfMW|v1_vAz1Aowg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4365537884326350996, 'MsgSeq': 871416351}
2025-08-01 09:50:39 | INFO | 收到文本消息: 消息ID:1438356712 来自:27852221909@chatroom 发送人:qq631390473 @:['wxid_5kipwrzramxr22'] 内容:@枂菟ིྀ 哪来的？
2025-08-01 09:50:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@枂菟ིྀ 哪来的？' from qq631390473 in 27852221909@chatroom
2025-08-01 09:50:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['@枂菟ིྀ\u2005哪来的？']
2025-08-01 09:50:39 | DEBUG | 处理消息内容: '@枂菟ིྀ 哪来的？'
2025-08-01 09:50:39 | DEBUG | 消息内容 '@枂菟ིྀ 哪来的？' 不匹配任何命令，忽略
2025-08-01 09:50:48 | DEBUG | 收到消息: {'MsgId': 442196154, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n肩视'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013053, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_zRtq/jQe|v1_7+M96h+I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1825543172745116414, 'MsgSeq': 871416352}
2025-08-01 09:50:48 | INFO | 收到文本消息: 消息ID:442196154 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 @:[] 内容:肩视
2025-08-01 09:50:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '肩视' from wxid_ohq9p1qosjzq22 in 27852221909@chatroom
2025-08-01 09:50:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['肩视']
2025-08-01 09:50:48 | DEBUG | 处理消息内容: '肩视'
2025-08-01 09:50:48 | DEBUG | 消息内容 '肩视' 不匹配任何命令，忽略
2025-08-01 09:50:50 | DEBUG | 收到消息: {'MsgId': 241857410, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n<msg><emoji fromusername="wxid_ohq9p1qosjzq22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="ae2fb113d3dfea37c64ffdda83e87641" len="12005" productid="com.tencent.xin.emoticon.person.stiker_152714822278d95f582c0c26ee" androidmd5="ae2fb113d3dfea37c64ffdda83e87641" androidlen="12005" s60v3md5="ae2fb113d3dfea37c64ffdda83e87641" s60v3len="12005" s60v5md5="ae2fb113d3dfea37c64ffdda83e87641" s60v5len="12005" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=ae2fb113d3dfea37c64ffdda83e87641&amp;filekey=30340201010420301e020201060402535a0410ae2fb113d3dfea37c64ffdda83e8764102022ee5040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373135323331343030306365386339393632656135353436623338356630393030303030313036&amp;bizid=1023" designerid="" thumburl="http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLAszSAQ4gJlh6P0CWaib8ScMYXGDCI6yjV9pdWoMNdTfEWkX1ymAia5I5/0" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=4dcd58d35a0306d70c65029cb39f5da3&amp;filekey=30340201010420301e020201060402535a04104dcd58d35a0306d70c65029cb39f5da302022ef0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373135323331343030306630363730393632656135353432363064356630393030303030313036&amp;bizid=1023" aeskey="87fbc9ee058536538635da01f7110860" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=c777858333c8db73320531708dadeaeb&amp;filekey=30340201010420301e020201060402535a0410c777858333c8db73320531708dadeaeb02022620040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373135323331353030303230383963393632656135353461363539356630393030303030313036&amp;bizid=1023" externmd5="baf70a653070d2c7d9675708114b7f01" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="Cg8KBXpoX2NuEgblk4jlk4gKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA="></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013055, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_tnpaxkGj|v1_ZfX/qKqR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7061102851176968877, 'MsgSeq': 871416353}
2025-08-01 09:50:50 | INFO | 收到表情消息: 消息ID:241857410 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 MD5:ae2fb113d3dfea37c64ffdda83e87641 大小:12005
2025-08-01 09:50:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7061102851176968877
2025-08-01 09:50:59 | DEBUG | 收到消息: {'MsgId': 2008047063, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n那是神器'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013065, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_7ccNaW0j|v1_euMuiIKk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2573673766698876479, 'MsgSeq': 871416354}
2025-08-01 09:50:59 | INFO | 收到文本消息: 消息ID:2008047063 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:那是神器
2025-08-01 09:50:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那是神器' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-01 09:50:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['那是神器']
2025-08-01 09:50:59 | DEBUG | 处理消息内容: '那是神器'
2025-08-01 09:50:59 | DEBUG | 消息内容 '那是神器' 不匹配任何命令，忽略
2025-08-01 09:51:20 | DEBUG | 收到消息: {'MsgId': 258715563, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>300换的那个是神器</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8855398848028283109</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_j2kp7us99uv222</chatusr>\n\t\t\t<displayname>EE11</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_EA/jOcyx|v1_b1hphLu1&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n这个背饰是能上天吗 300个换</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754013031</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_2530z9t0joek22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013085, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>c582365991bc1c5737a57c64109a9743_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Bunmo7vA|v1_K/FJE3PM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4312707348402383701, 'MsgSeq': 871416355}
2025-08-01 09:51:20 | DEBUG | 从群聊消息中提取发送者: wxid_2530z9t0joek22
2025-08-01 09:51:20 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:51:20 | INFO | 收到引用消息: 消息ID:258715563 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 内容:300换的那个是神器 引用类型:1
2025-08-01 09:51:20 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:51:20 | INFO | [DouBaoImageToImage] 消息内容: '300换的那个是神器' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-01 09:51:20 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['300换的那个是神器']
2025-08-01 09:51:20 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 09:51:20 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:51:20 | INFO |   - 消息内容: 300换的那个是神器
2025-08-01 09:51:20 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-01 09:51:20 | INFO |   - 发送人: wxid_2530z9t0joek22
2025-08-01 09:51:20 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n这个背饰是能上天吗 300个换', 'Msgid': '8855398848028283109', 'NewMsgId': '8855398848028283109', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'EE11', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_EA/jOcyx|v1_b1hphLu1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754013031', 'SenderWxid': 'wxid_2530z9t0joek22'}
2025-08-01 09:51:20 | INFO |   - 引用消息ID: 
2025-08-01 09:51:20 | INFO |   - 引用消息类型: 
2025-08-01 09:51:20 | INFO |   - 引用消息内容: 
这个背饰是能上天吗 300个换
2025-08-01 09:51:20 | INFO |   - 引用消息发送人: wxid_2530z9t0joek22
2025-08-01 09:51:40 | DEBUG | 收到消息: {'MsgId': 1464416151, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n@\xa0执傲\u2005周年庆里面，右下角，周年兑换'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013105, 'MsgSource': '<msgsource>\n\t<atuserlist>qq631390473</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Ijd1EAIp|v1_zapAxua4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8779879286423177273, 'MsgSeq': 871416356}
2025-08-01 09:51:40 | INFO | 收到文本消息: 消息ID:1464416151 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:['qq631390473'] 内容:@ 执傲 周年庆里面，右下角，周年兑换
2025-08-01 09:51:40 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@ 执傲 周年庆里面，右下角，周年兑换' from wxid_5kipwrzramxr22 in 27852221909@chatroom
2025-08-01 09:51:40 | DEBUG | [DouBaoImageToImage] 命令解析: ['@\xa0执傲\u2005周年庆里面，右下角，周年兑换']
2025-08-01 09:51:40 | DEBUG | 处理消息内容: '@ 执傲 周年庆里面，右下角，周年兑换'
2025-08-01 09:51:40 | DEBUG | 消息内容 '@ 执傲 周年庆里面，右下角，周年兑换' 不匹配任何命令，忽略
2025-08-01 09:51:42 | DEBUG | 收到消息: {'MsgId': 459015635, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_j2kp7us99uv222:\n没啥印象这个是什么样子的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013106, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_FNFrbjEg|v1_BQX7Cnms</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7212738867590487800, 'MsgSeq': 871416357}
2025-08-01 09:51:42 | INFO | 收到文本消息: 消息ID:459015635 来自:27852221909@chatroom 发送人:wxid_j2kp7us99uv222 @:[] 内容:没啥印象这个是什么样子的
2025-08-01 09:51:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没啥印象这个是什么样子的' from wxid_j2kp7us99uv222 in 27852221909@chatroom
2025-08-01 09:51:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['没啥印象这个是什么样子的']
2025-08-01 09:51:42 | DEBUG | 处理消息内容: '没啥印象这个是什么样子的'
2025-08-01 09:51:42 | DEBUG | 消息内容 '没啥印象这个是什么样子的' 不匹配任何命令，忽略
2025-08-01 09:52:33 | DEBUG | 收到消息: {'MsgId': 371828319, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="b167fcdba4fbe58a8db6a6355693dd30" encryver="1" cdnthumbaeskey="b167fcdba4fbe58a8db6a6355693dd30" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1de6042434346462343337622d653965662d346562372d626365332d386634366437366566303039020405250a020201000405004c51e500" cdnthumblength="4081" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1de6042434346462343337622d653965662d346562372d626365332d386634366437366566303039020405250a020201000405004c51e500" length="750228" md5="f406db82d2c18752bf0ea15b1824819c" hevc_mid_size="69437" originsourcemd5="c8aef11b4ee08557bb8a8c2e589571a9">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImI2ZDAxMDIwMzQ3MDUwMDAiLCJwZHFIYXNoIjoiMDRlZDU5MWVmZmQwNzJkNjBi\nOWVlMGMxNzcyYjFkMTUxZWNmMjYyZDI5YWYyNzI5MjUyZjE1ZTk4Yzg1Y2ExNiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013158, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>77cfcc522ff3bd754dfbdca59b31ba38_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_oqT1GJUG|v1_vvYBojZZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4042058371658847697, 'MsgSeq': 871416358}
2025-08-01 09:52:33 | INFO | 收到图片消息: 消息ID:371828319 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="b167fcdba4fbe58a8db6a6355693dd30" encryver="1" cdnthumbaeskey="b167fcdba4fbe58a8db6a6355693dd30" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1de6042434346462343337622d653965662d346562372d626365332d386634366437366566303039020405250a020201000405004c51e500" cdnthumblength="4081" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1de6042434346462343337622d653965662d346562372d626365332d386634366437366566303039020405250a020201000405004c51e500" length="750228" md5="f406db82d2c18752bf0ea15b1824819c" hevc_mid_size="69437" originsourcemd5="c8aef11b4ee08557bb8a8c2e589571a9"><secHashInfoBase64>eyJwaGFzaCI6ImI2ZDAxMDIwMzQ3MDUwMDAiLCJwZHFIYXNoIjoiMDRlZDU5MWVmZmQwNzJkNjBiOWVlMGMxNzcyYjFkMTUxZWNmMjYyZDI5YWYyNzI5MjUyZjE1ZTk4Yzg1Y2ExNiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:52:33 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:52:33 | INFO | [TimerTask] 缓存图片消息: 371828319
2025-08-01 09:52:42 | DEBUG | 收到消息: {'MsgId': 1342677644, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n哇塞'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013167, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_uwRf7gvM|v1_c/BJHhSV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3603303749340387485, 'MsgSeq': 871416359}
2025-08-01 09:52:42 | INFO | 收到文本消息: 消息ID:1342677644 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 @:[] 内容:哇塞
2025-08-01 09:52:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哇塞' from wxid_ohq9p1qosjzq22 in 27852221909@chatroom
2025-08-01 09:52:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['哇塞']
2025-08-01 09:52:42 | DEBUG | 处理消息内容: '哇塞'
2025-08-01 09:52:42 | DEBUG | 消息内容 '哇塞' 不匹配任何命令，忽略
2025-08-01 09:52:45 | DEBUG | 收到消息: {'MsgId': 897004354, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="febb5d2bafdcbef4be7142f2d08c5f43" encryver="1" cdnthumbaeskey="febb5d2bafdcbef4be7142f2d08c5f43" cdnthumburl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1df2042465343631653930332d636233392d343237342d386539322d646661386363343437326263020405290a020201000405004c57c100" cdnthumblength="2767" cdnthumbheight="192" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1df2042465343631653930332d636233392d343237342d386539322d646661386363343437326263020405290a020201000405004c57c100" length="824614" md5="818b8d5733856850f001dcca4c0ff271" hevc_mid_size="60670" originsourcemd5="26c2dfb6f96f11e1b947d0233791726e">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6Ijc1MTA1MDEwMDAwMDQ1NDAiLCJwZHFIYXNoIjoiMTM3N2NjZTQzOWIzMzMzM2U2\nY2FkMzMzY2NjZDM3MzQwY2RkMGNjYzgzMzNjOWRmMzIwMmYzY2RjMjMzMjkyMSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013170, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>b252a400b0d6953098a4cc62f8189cf3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_6yytUfg/|v1_LI2urkdT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 117059153720233009, 'MsgSeq': 871416360}
2025-08-01 09:52:45 | INFO | 收到图片消息: 消息ID:897004354 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 XML:<?xml version="1.0"?><msg><img aeskey="febb5d2bafdcbef4be7142f2d08c5f43" encryver="1" cdnthumbaeskey="febb5d2bafdcbef4be7142f2d08c5f43" cdnthumburl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1df2042465343631653930332d636233392d343237342d386539322d646661386363343437326263020405290a020201000405004c57c100" cdnthumblength="2767" cdnthumbheight="192" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1df2042465343631653930332d636233392d343237342d386539322d646661386363343437326263020405290a020201000405004c57c100" length="824614" md5="818b8d5733856850f001dcca4c0ff271" hevc_mid_size="60670" originsourcemd5="26c2dfb6f96f11e1b947d0233791726e"><secHashInfoBase64>eyJwaGFzaCI6Ijc1MTA1MDEwMDAwMDQ1NDAiLCJwZHFIYXNoIjoiMTM3N2NjZTQzOWIzMzMzM2U2Y2FkMzMzY2NjZDM3MzQwY2RkMGNjYzgzMzNjOWRmMzIwMmYzY2RjMjMzMjkyMSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:52:45 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:52:45 | INFO | [TimerTask] 缓存图片消息: 897004354
2025-08-01 09:52:47 | DEBUG | 收到消息: {'MsgId': 1889467584, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qq631390473:\n@枂菟ིྀ\u2005哦了，@慕ؓ悦ؓ˒\u2005给我换一个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013173, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_5kipwrzramxr22,wxid_2530z9t0joek22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_V2Px83W9|v1_+LXuwN36</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3743795783958944413, 'MsgSeq': 871416361}
2025-08-01 09:52:47 | INFO | 收到文本消息: 消息ID:1889467584 来自:27852221909@chatroom 发送人:qq631390473 @:['wxid_5kipwrzramxr22', 'wxid_2530z9t0joek22'] 内容:@枂菟ིྀ 哦了，@慕ؓ悦ؓ˒ 给我换一个
2025-08-01 09:52:47 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@枂菟ིྀ 哦了，@慕ؓ悦ؓ˒ 给我换一个' from qq631390473 in 27852221909@chatroom
2025-08-01 09:52:47 | DEBUG | [DouBaoImageToImage] 命令解析: ['@枂菟ིྀ\u2005哦了，@慕ؓ悦ؓ˒\u2005给我换一个']
2025-08-01 09:52:47 | DEBUG | 处理消息内容: '@枂菟ིྀ 哦了，@慕ؓ悦ؓ˒ 给我换一个'
2025-08-01 09:52:47 | DEBUG | 消息内容 '@枂菟ིྀ 哦了，@慕ؓ悦ؓ˒ 给我换一个' 不匹配任何命令，忽略
2025-08-01 09:53:12 | DEBUG | 收到消息: {'MsgId': 584034332, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@\xa0执傲\u2005换啥'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013198, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[qq631390473]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_m6tCSz4Y|v1_z92hJmGp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1469988639060327664, 'MsgSeq': 871416362}
2025-08-01 09:53:12 | INFO | 收到文本消息: 消息ID:584034332 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['qq631390473'] 内容:@ 执傲 换啥
2025-08-01 09:53:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@ 执傲 换啥' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-01 09:53:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['@\xa0执傲\u2005换啥']
2025-08-01 09:53:12 | DEBUG | 处理消息内容: '@ 执傲 换啥'
2025-08-01 09:53:12 | DEBUG | 消息内容 '@ 执傲 换啥' 不匹配任何命令，忽略
2025-08-01 09:54:57 | DEBUG | 收到消息: {'MsgId': 1559810844, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d275a4912df96aa57e04b32c71145b75" encryver="1" cdnthumbaeskey="d275a4912df96aa57e04b32c71145b75" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1e76042466656334316139322d643937322d346236362d393036612d363237633939633431366564020405250a020201000405004c4e6100" cdnthumblength="3594" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1e76042466656334316139322d643937322d346236362d393036612d363237633939633431366564020405250a020201000405004c4e6100" length="653963" md5="872fe29fe00c58a7fa77b22f9077c89f" hevc_mid_size="64632" originsourcemd5="6d4d1877cdc40151fd5fe5f9e4d6135c">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImI2ZDEwMDYxMzYwMDAwMDAiLCJwZHFIYXNoIjoiMzFiOTQ4NjZmZjUwMzA5NmNh\nNzIzMzMyZDljZjQ1OTUzMzY3YThjZDA5YWZiZDI5MzUyNzBkMTlhYzI1N2JjNiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013302, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9111089af90c58158a34039dbee95b12_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_9IeckF5k|v1_WSRjizoV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 672220077356173430, 'MsgSeq': 871416363}
2025-08-01 09:54:57 | INFO | 收到图片消息: 消息ID:1559810844 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="d275a4912df96aa57e04b32c71145b75" encryver="1" cdnthumbaeskey="d275a4912df96aa57e04b32c71145b75" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1e76042466656334316139322d643937322d346236362d393036612d363237633939633431366564020405250a020201000405004c4e6100" cdnthumblength="3594" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1e76042466656334316139322d643937322d346236362d393036612d363237633939633431366564020405250a020201000405004c4e6100" length="653963" md5="872fe29fe00c58a7fa77b22f9077c89f" hevc_mid_size="64632" originsourcemd5="6d4d1877cdc40151fd5fe5f9e4d6135c"><secHashInfoBase64>eyJwaGFzaCI6ImI2ZDEwMDYxMzYwMDAwMDAiLCJwZHFIYXNoIjoiMzFiOTQ4NjZmZjUwMzA5NmNhNzIzMzMyZDljZjQ1OTUzMzY3YThjZDA5YWZiZDI5MzUyNzBkMTlhYzI1N2JjNiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:54:57 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:54:57 | INFO | [TimerTask] 缓存图片消息: 1559810844
2025-08-01 09:55:54 | DEBUG | 收到消息: {'MsgId': 1534659833, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="68726881a46b93686db130e0892a8b3f" encryver="1" cdnthumbaeskey="68726881a46b93686db130e0892a8b3f" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" cdnthumblength="5646" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" length="1410600" md5="18cb9544468423954b037d1ae0bec637" hevc_mid_size="168223" originsourcemd5="c8e38ce579d1c81d68ca79543a134133">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImQ1YjBlYjAyMDAwNDAyMDAiLCJwZHFIYXNoIjoiNDQwZDY4YjQ4NzZiZTkxOTBl\nNGY5MzQ2Zjg3NDJmYzY2ZmQxMjIzY2JiMDk5YzdlYTNjZTdkYzY2NjM5NDgwYiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013359, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>1da5042d02e9fbdb34f1b0f26c2e498d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_5PdPJINy|v1_O6MMf3qK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3609996522958101020, 'MsgSeq': 871416364}
2025-08-01 09:55:54 | INFO | 收到图片消息: 消息ID:1534659833 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="68726881a46b93686db130e0892a8b3f" encryver="1" cdnthumbaeskey="68726881a46b93686db130e0892a8b3f" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" cdnthumblength="5646" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" length="1410600" md5="18cb9544468423954b037d1ae0bec637" hevc_mid_size="168223" originsourcemd5="c8e38ce579d1c81d68ca79543a134133"><secHashInfoBase64>eyJwaGFzaCI6ImQ1YjBlYjAyMDAwNDAyMDAiLCJwZHFIYXNoIjoiNDQwZDY4YjQ4NzZiZTkxOTBlNGY5MzQ2Zjg3NDJmYzY2ZmQxMjIzY2JiMDk5YzdlYTNjZTdkYzY2NjM5NDgwYiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:55:54 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:55:54 | INFO | [TimerTask] 缓存图片消息: 1534659833
2025-08-01 09:56:13 | DEBUG | 收到消息: {'MsgId': 353954382, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n想弄这个，但又好贵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013378, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Jp0+N4OR|v1_ouvfMI19</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7648652598302384420, 'MsgSeq': 871416365}
2025-08-01 09:56:13 | INFO | 收到文本消息: 消息ID:353954382 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:想弄这个，但又好贵
2025-08-01 09:56:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '想弄这个，但又好贵' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-01 09:56:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['想弄这个，但又好贵']
2025-08-01 09:56:13 | DEBUG | 处理消息内容: '想弄这个，但又好贵'
2025-08-01 09:56:13 | DEBUG | 消息内容 '想弄这个，但又好贵' 不匹配任何命令，忽略
2025-08-01 09:56:31 | DEBUG | 收到消息: {'MsgId': 348948674, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="b362fb712a658afa2aff888037aee020" encryver="1" cdnthumbaeskey="b362fb712a658afa2aff888037aee020" cdnthumburl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1ed4042432643338653234302d356638302d346463652d623138342d393735336166613361613934020405290a020201000405004c57c100" cdnthumblength="4039" cdnthumbheight="192" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1ed4042432643338653234302d356638302d346463652d623138342d393735336166613361613934020405290a020201000405004c57c100" length="1261887" md5="564efa2a4fa27f862061c913f082dcaf" hevc_mid_size="115137" originsourcemd5="58214d4c1e32278f53333d813416a8d7">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjVhMDAxMDgwMjAwMDAwMDAiLCJwZHFIYXNoIjoiNWRiNTg2ZGRhMTY3YWExZDJh\nMmQzNDE0YTk2MTdjZWM4ZTllNTNhOGY5NDIzNDU2M2U1MmRiYmE0MzcxYThjNSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013396, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>50c521c22ca0f253d11c62aeecd926eb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_+RtPCerp|v1_DqB0oD7a</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6286722348321601222, 'MsgSeq': 871416366}
2025-08-01 09:56:31 | INFO | 收到图片消息: 消息ID:348948674 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 XML:<?xml version="1.0"?><msg><img aeskey="b362fb712a658afa2aff888037aee020" encryver="1" cdnthumbaeskey="b362fb712a658afa2aff888037aee020" cdnthumburl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1ed4042432643338653234302d356638302d346463652d623138342d393735336166613361613934020405290a020201000405004c57c100" cdnthumblength="4039" cdnthumbheight="192" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002042460f96602034c57c10204a383e17c0204688c1ed4042432643338653234302d356638302d346463652d623138342d393735336166613361613934020405290a020201000405004c57c100" length="1261887" md5="564efa2a4fa27f862061c913f082dcaf" hevc_mid_size="115137" originsourcemd5="58214d4c1e32278f53333d813416a8d7"><secHashInfoBase64>eyJwaGFzaCI6IjVhMDAxMDgwMjAwMDAwMDAiLCJwZHFIYXNoIjoiNWRiNTg2ZGRhMTY3YWExZDJhMmQzNDE0YTk2MTdjZWM4ZTllNTNhOGY5NDIzNDU2M2U1MmRiYmE0MzcxYThjNSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 09:56:31 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-01 09:56:31 | INFO | [TimerTask] 缓存图片消息: 348948674
2025-08-01 09:56:43 | DEBUG | 收到消息: {'MsgId': 1969768421, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n这都不用玩？直接领了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013408, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_DJQJXVld|v1_S+BhWVla</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 250100031584383119, 'MsgSeq': 871416367}
2025-08-01 09:56:43 | INFO | 收到文本消息: 消息ID:1969768421 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 @:[] 内容:这都不用玩？直接领了
2025-08-01 09:56:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这都不用玩？直接领了' from wxid_ohq9p1qosjzq22 in 27852221909@chatroom
2025-08-01 09:56:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['这都不用玩？直接领了']
2025-08-01 09:56:43 | DEBUG | 处理消息内容: '这都不用玩？直接领了'
2025-08-01 09:56:43 | DEBUG | 消息内容 '这都不用玩？直接领了' 不匹配任何命令，忽略
2025-08-01 09:56:48 | DEBUG | 收到消息: {'MsgId': 917895143, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>弄</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>3609996522958101020</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>慕ؓ悦ؓ˒</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="68726881a46b93686db130e0892a8b3f" encryver="1" cdnthumbaeskey="68726881a46b93686db130e0892a8b3f" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" cdnthumblength="5646" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" length="1410600" md5="18cb9544468423954b037d1ae0bec637" hevc_mid_size="168223" originsourcemd5="c8e38ce579d1c81d68ca79543a134133"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImQ1YjBlYjAyMDAwNDAyMDAiLCJwZHFIYXNoIjoiNDQwZDY4YjQ4NzZiZTkxOTBl\nNGY5MzQ2Zjg3NDJmYzY2ZmQxMjIzY2JiMDk5YzdlYTNjZTdkYzY2NjM5NDgwYiJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777825852&lt;/sequence_id&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;1da5042d02e9fbdb34f1b0f26c2e498d_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="168223" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_7FiUolIk|v1_HV7Ih+GA&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754013359</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013414, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>b74c34f6266225c4321d69981bf5233b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_ZxxC28rM|v1_CgweneEZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8172348679479079413, 'MsgSeq': 871416368}
2025-08-01 09:56:48 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-01 09:56:48 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:56:48 | INFO | 收到引用消息: 消息ID:917895143 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:弄 引用类型:3
2025-08-01 09:56:48 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:56:48 | INFO | [DouBaoImageToImage] 消息内容: '弄' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-01 09:56:48 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['弄']
2025-08-01 09:56:48 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 09:56:48 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:56:48 | INFO |   - 消息内容: 弄
2025-08-01 09:56:48 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-01 09:56:48 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-01 09:56:48 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>弄</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>3609996522958101020</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>慕ؓ悦ؓ˒</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="68726881a46b93686db130e0892a8b3f" encryver="1" cdnthumbaeskey="68726881a46b93686db130e0892a8b3f" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" cdnthumblength="5646" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" length="1410600" md5="18cb9544468423954b037d1ae0bec637" hevc_mid_size="168223" originsourcemd5="c8e38ce579d1c81d68ca79543a134133"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImQ1YjBlYjAyMDAwNDAyMDAiLCJwZHFIYXNoIjoiNDQwZDY4YjQ4NzZiZTkxOTBl\nNGY5MzQ2Zjg3NDJmYzY2ZmQxMjIzY2JiMDk5YzdlYTNjZTdkYzY2NjM5NDgwYiJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777825852&lt;/sequence_id&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;1da5042d02e9fbdb34f1b0f26c2e498d_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="168223" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_7FiUolIk|v1_HV7Ih+GA&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754013359</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '3609996522958101020', 'NewMsgId': '3609996522958101020', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '慕ؓ悦ؓ˒', 'MsgSource': '<msgsource><sequence_id>777825852</sequence_id>\n\t<sec_msg_node>\n\t\t<uuid>1da5042d02e9fbdb34f1b0f26c2e498d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="168223" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_7FiUolIk|v1_HV7Ih+GA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754013359', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-01 09:56:48 | INFO |   - 引用消息ID: 
2025-08-01 09:56:48 | INFO |   - 引用消息类型: 
2025-08-01 09:56:48 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>弄</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>3609996522958101020</svrid>
			<fromusr>27852221909@chatroom</fromusr>
			<chatusr>wxid_2530z9t0joek22</chatusr>
			<displayname>慕ؓ悦ؓ˒</displayname>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="68726881a46b93686db130e0892a8b3f" encryver="1" cdnthumbaeskey="68726881a46b93686db130e0892a8b3f" cdnthumburl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" cdnthumblength="5646" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f5dc90204a5c6e17c0204688c1eaf042439353835323430632d333837322d343436362d393436302d346337333730386461343763020405250a020201000405004c50b900" length="1410600" md5="18cb9544468423954b037d1ae0bec637" hevc_mid_size="168223" originsourcemd5="c8e38ce579d1c81d68ca79543a134133"&gt;
		&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImQ1YjBlYjAyMDAwNDAyMDAiLCJwZHFIYXNoIjoiNDQwZDY4YjQ4NzZiZTkxOTBl
NGY5MzQ2Zjg3NDJmYzY2ZmQxMjIzY2JiMDk5YzdlYTNjZTdkYzY2NjM5NDgwYiJ9
&lt;/secHashInfoBase64&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777825852&lt;/sequence_id&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;1da5042d02e9fbdb34f1b0f26c2e498d_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="168223" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;145&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_7FiUolIk|v1_HV7Ih+GA&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1754013359</createtime>
		</refermsg>
	</appmsg>
	<fromusername>wxid_c3jkq1ylevnb12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-01 09:56:48 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-01 09:57:08 | DEBUG | 收到消息: {'MsgId': 1192464206, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n这个估计得一万多钻'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013434, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_c/F5G7jh|v1_eNZkftkr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3931842247780882252, 'MsgSeq': 871416369}
2025-08-01 09:57:08 | INFO | 收到文本消息: 消息ID:1192464206 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:这个估计得一万多钻
2025-08-01 09:57:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这个估计得一万多钻' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-01 09:57:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['这个估计得一万多钻']
2025-08-01 09:57:08 | DEBUG | 处理消息内容: '这个估计得一万多钻'
2025-08-01 09:57:08 | DEBUG | 消息内容 '这个估计得一万多钻' 不匹配任何命令，忽略
2025-08-01 09:59:02 | DEBUG | 收到消息: {'MsgId': 1236180702, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n这么大的记录，是真的全部都有用吗[发呆]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013547, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_R82yfiqA|v1_f9Z+Ry2a</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 这么大的记录，是真的全部都有用吗[发呆]', 'NewMsgId': 440708322565824566, 'MsgSeq': 871416370}
2025-08-01 09:59:02 | INFO | 收到文本消息: 消息ID:1236180702 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:这么大的记录，是真的全部都有用吗[发呆]
2025-08-01 09:59:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这么大的记录，是真的全部都有用吗[发呆]' from seraph333 in 47325400669@chatroom
2025-08-01 09:59:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['这么大的记录，是真的全部都有用吗[发呆]']
2025-08-01 09:59:02 | DEBUG | 处理消息内容: '这么大的记录，是真的全部都有用吗[发呆]'
2025-08-01 09:59:02 | DEBUG | 消息内容 '这么大的记录，是真的全部都有用吗[发呆]' 不匹配任何命令，忽略
2025-08-01 09:59:06 | DEBUG | 收到消息: {'MsgId': 99351011, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_besewpsontwy29:\n谁有L'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013552, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_FQQSmNHF|v1_8BD8bJk9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2307685962231919372, 'MsgSeq': 871416371}
2025-08-01 09:59:06 | INFO | 收到文本消息: 消息ID:99351011 来自:27852221909@chatroom 发送人:wxid_besewpsontwy29 @:[] 内容:谁有L
2025-08-01 09:59:06 | DEBUG | [DouBaoImageToImage] 收到文本消息: '谁有L' from wxid_besewpsontwy29 in 27852221909@chatroom
2025-08-01 09:59:06 | DEBUG | [DouBaoImageToImage] 命令解析: ['谁有L']
2025-08-01 09:59:06 | DEBUG | 处理消息内容: '谁有L'
2025-08-01 09:59:06 | DEBUG | 消息内容 '谁有L' 不匹配任何命令，忽略
2025-08-01 09:59:19 | DEBUG | 收到消息: {'MsgId': 1485885073, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x95sfijdz8xy22:\n好搞笑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013565, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_bXVm9DSr|v1_lPS4/KFc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4151404501419515165, 'MsgSeq': 871416372}
2025-08-01 09:59:19 | INFO | 收到文本消息: 消息ID:1485885073 来自:27852221909@chatroom 发送人:wxid_x95sfijdz8xy22 @:[] 内容:好搞笑
2025-08-01 09:59:19 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好搞笑' from wxid_x95sfijdz8xy22 in 27852221909@chatroom
2025-08-01 09:59:19 | DEBUG | [DouBaoImageToImage] 命令解析: ['好搞笑']
2025-08-01 09:59:19 | DEBUG | 处理消息内容: '好搞笑'
2025-08-01 09:59:19 | DEBUG | 消息内容 '好搞笑' 不匹配任何命令，忽略
2025-08-01 09:59:32 | DEBUG | 收到消息: {'MsgId': 1900085007, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_x95sfijdz8xy22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="3020abe1153103b203d4d80f4b120489" cdnvideourl="3057020100044b30490201000204013a669602032e6c6302048dab016f0204688c1f7d042462356139663362332d343861342d343135372d396361632d33336134363063303636636602040d2808040201000405004c57c300" cdnthumbaeskey="3020abe1153103b203d4d80f4b120489" cdnthumburl="3057020100044b30490201000204013a669602032e6c6302048dab016f0204688c1f7d042462356139663362332d343861342d343135372d396361632d33336134363063303636636602040d2808040201000405004c57c300" length="888136" playlength="6" cdnthumblength="8737" cdnthumbwidth="640" cdnthumbheight="288" fromusername="wxid_x95sfijdz8xy22" md5="91321f1a9212d6def089a6925fa5bf59" newmd5="762dd49d28cea40c897942112f607536" isplaceholder="0" rawmd5="f07a90269ff1a686faf2c2262e0d17df" rawlength="7803919" cdnrawvideourl="3057020100044b30490201000204d6b81a9802032e6c63020487ab016f0204688c1f82042438636637313739362d393935652d343563322d383764622d6632336465643430363561340204059800040201000405004c4d3700" cdnrawvideoaeskey="6e10a917a384006c44b99d10b14f91d0" overwritenewmsgid="0" originsourcemd5="a1be94c5e3c35fcca6bf05ea33a49790" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013578, 'MsgSource': '<msgsource>\n\t<videopreloadlen>750372</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>21f0d9bc4d688bd02c53060c00a7b7a7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_L2hr6UQ+|v1_sRueSQVt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6852473101905302583, 'MsgSeq': 871416373}
2025-08-01 09:59:32 | INFO | 收到视频消息: 消息ID:1900085007 来自:27852221909@chatroom 发送人:wxid_x95sfijdz8xy22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="3020abe1153103b203d4d80f4b120489" cdnvideourl="3057020100044b30490201000204013a669602032e6c6302048dab016f0204688c1f7d042462356139663362332d343861342d343135372d396361632d33336134363063303636636602040d2808040201000405004c57c300" cdnthumbaeskey="3020abe1153103b203d4d80f4b120489" cdnthumburl="3057020100044b30490201000204013a669602032e6c6302048dab016f0204688c1f7d042462356139663362332d343861342d343135372d396361632d33336134363063303636636602040d2808040201000405004c57c300" length="888136" playlength="6" cdnthumblength="8737" cdnthumbwidth="640" cdnthumbheight="288" fromusername="wxid_x95sfijdz8xy22" md5="91321f1a9212d6def089a6925fa5bf59" newmd5="762dd49d28cea40c897942112f607536" isplaceholder="0" rawmd5="f07a90269ff1a686faf2c2262e0d17df" rawlength="7803919" cdnrawvideourl="3057020100044b30490201000204d6b81a9802032e6c63020487ab016f0204688c1f82042438636637313739362d393935652d343563322d383764622d6632336465643430363561340204059800040201000405004c4d3700" cdnrawvideoaeskey="6e10a917a384006c44b99d10b14f91d0" overwritenewmsgid="0" originsourcemd5="a1be94c5e3c35fcca6bf05ea33a49790" isad="0" />
</msg>

2025-08-01 10:04:16 | DEBUG | 收到消息: {'MsgId': 988122787, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14713407379855509650</objectId>\n\t\t\t<objectNonceId>14721346261057971314_4_20_13_1_1754013687666225_6f5b0b80-6e7b-11f0-a487-a957b245ab8c</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>纯爱暴富和躺平</nickname>\n\t\t\t<username>v2_060000231003b20faec8cae0811ac7d4cf07ee3db0779ef103d8ea7691692af32226ab33f0ce@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/S3ibiaFJyq2ibpQNdSbPEdPSZGSRiaRS67Lmq2HhcDE3CRVuC30sFVcuexd8Lj4VhdTQiabLnnjv8vKSnY3xCuOjKKHtfgcB3qOJAlj8yaXFNkAGecKdPAPoVWPxdicqWAMicCF/0]]></avatar>\n\t\t\t<desc>好奇[发呆]法庭上真这样嘛\n#搞笑#真的吗#八卦#好奇</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8ZnbQszuiaqNnMj3qZJrXJAIQ0N7Y8x0BXGWFica7JbGKcJsCFL7cGDqDoYu3eic4ytt130IP6ZdpXgg7HH7UicPqxXNam5jwttK7IQVAxcvh4wPls&hy=SZ&idx=1&m=f4f5020c674f0103082b4bcda720de49&uzid=7a206&token=6xykWLEnztJD1V02HxcJfcF9CUjFJDXfbNkjq5c11Yia65RUZTMDJLHAk1DCDH3eE0px3DqV57Le6f7icjFYko6P9bnOG1aPI8ZkkW1qibr25Gd2AX2IypsrCBLLTXPeA8RDjWgDicVKZ7D0ia3z017V0YT0KRpKcte94tz7Mlm0UXjk&basedata=CAESBnhXVDEyNhoGeFdUMTI2GgZ4V1QxMjcaBnhXVDExMRoGeFdUMTI4IgwKCgoGeFdUMTExEAEqBwi9HhAAGAI&sign=K2j9ViLnwan-s2S5eTJkigfDUrWf39zelrQx1v5jad5IQSU-60wVFiJgUmmkrPuxCb6N8XRDViyY12ZS54KvGg&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAABhVwxYNp5%2B26QK%2Bh%2BMaBAAAADnaHZTnGbFfAj9RgZXfw6VmVOk83whQgq7g498E%2FapmFSavOq4gkfJW%2FR7lId%2FXdOSrA1uIlarvYU%3D&svrnonce=1754013690]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Znxupt2jaibWrxAufSqUdVnljwlSGhoSZW7pHicYI2IbWFpp7xbmJ9476fuTmNcKW3EMNEkayicKibJsDBcg2bIlVfWB444DojuVbElgdicib3jHBEg&hy=SZ&idx=1&m=fbeac8323f87a00e2ea1e6032368d9fe&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eEyIrD6jL1CZCQybuGRJLqkBzKFia8gTibOjhb8RZWYClV9pgPpeyMEyESGA5kYDobDWaSIDticIp2VYnfmb69adfe4CK79dvl3kd7icicgg4piccapovzPr14zbZquB1wsD78s933rNveprH7d&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Znxupt2jaibWrxAufSqUdVnljwlSGhoSZW7pHicYI2IbWFpp7xbmJ9476fuTmNcKW3EMNEkayicKibJsDBcg2bIlVfWB444DojuVbElgdicib3jHBEg&hy=SZ&idx=1&m=fbeac8323f87a00e2ea1e6032368d9fe&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eEyIrD6jL1CZCQybuGRJLqkBzKFia8gTibOjhb8RZWYClV9pgPpeyMEyESGA5kYDobDWaSIDticIp2VYnfmb69adfe4CK79dvl3kd7icicgg4piccapovzPr14zbZquB1wsD78s933rNveprH7d&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[[0.0,0.0,0.0,0.0]]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>18</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754013689789","contextId":"1-1-20-e6647cbcc0d94f33875606aaa13aaf19","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013862, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>a753d219c368b089157afff1fa100039_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_ROlol7+O|v1_/oGbyjUT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 2043515458652006579, 'MsgSeq': 871416374}
2025-08-01 10:04:16 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-08-01 10:04:16 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713407379855509650</objectId>
			<objectNonceId>14721346261057971314_4_20_13_1_1754013687666225_6f5b0b80-6e7b-11f0-a487-a957b245ab8c</objectNonceId>
			<feedType>4</feedType>
			<nickname>纯爱暴富和躺平</nickname>
			<username>v2_060000231003b20faec8cae0811ac7d4cf07ee3db0779ef103d8ea7691692af32226ab33f0ce@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/S3ibiaFJyq2ibpQNdSbPEdPSZGSRiaRS67Lmq2HhcDE3CRVuC30sFVcuexd8Lj4VhdTQiabLnnjv8vKSnY3xCuOjKKHtfgcB3qOJAlj8yaXFNkAGecKdPAPoVWPxdicqWAMicCF/0]]></avatar>
			<desc>好奇[发呆]法庭上真这样嘛
#搞笑#真的吗#八卦#好奇</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8ZnbQszuiaqNnMj3qZJrXJAIQ0N7Y8x0BXGWFica7JbGKcJsCFL7cGDqDoYu3eic4ytt130IP6ZdpXgg7HH7UicPqxXNam5jwttK7IQVAxcvh4wPls&hy=SZ&idx=1&m=f4f5020c674f0103082b4bcda720de49&uzid=7a206&token=6xykWLEnztJD1V02HxcJfcF9CUjFJDXfbNkjq5c11Yia65RUZTMDJLHAk1DCDH3eE0px3DqV57Le6f7icjFYko6P9bnOG1aPI8ZkkW1qibr25Gd2AX2IypsrCBLLTXPeA8RDjWgDicVKZ7D0ia3z017V0YT0KRpKcte94tz7Mlm0UXjk&basedata=CAESBnhXVDEyNhoGeFdUMTI2GgZ4V1QxMjcaBnhXVDExMRoGeFdUMTI4IgwKCgoGeFdUMTExEAEqBwi9HhAAGAI&sign=K2j9ViLnwan-s2S5eTJkigfDUrWf39zelrQx1v5jad5IQSU-60wVFiJgUmmkrPuxCb6N8XRDViyY12ZS54KvGg&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAABhVwxYNp5%2B26QK%2Bh%2BMaBAAAADnaHZTnGbFfAj9RgZXfw6VmVOk83whQgq7g498E%2FapmFSavOq4gkfJW%2FR7lId%2FXdOSrA1uIlarvYU%3D&svrnonce=1754013690]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Znxupt2jaibWrxAufSqUdVnljwlSGhoSZW7pHicYI2IbWFpp7xbmJ9476fuTmNcKW3EMNEkayicKibJsDBcg2bIlVfWB444DojuVbElgdicib3jHBEg&hy=SZ&idx=1&m=fbeac8323f87a00e2ea1e6032368d9fe&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eEyIrD6jL1CZCQybuGRJLqkBzKFia8gTibOjhb8RZWYClV9pgPpeyMEyESGA5kYDobDWaSIDticIp2VYnfmb69adfe4CK79dvl3kd7icicgg4piccapovzPr14zbZquB1wsD78s933rNveprH7d&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Znxupt2jaibWrxAufSqUdVnljwlSGhoSZW7pHicYI2IbWFpp7xbmJ9476fuTmNcKW3EMNEkayicKibJsDBcg2bIlVfWB444DojuVbElgdicib3jHBEg&hy=SZ&idx=1&m=fbeac8323f87a00e2ea1e6032368d9fe&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eEyIrD6jL1CZCQybuGRJLqkBzKFia8gTibOjhb8RZWYClV9pgPpeyMEyESGA5kYDobDWaSIDticIp2VYnfmb69adfe4CK79dvl3kd7icicgg4piccapovzPr14zbZquB1wsD78s933rNveprH7d&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[[0.0,0.0,0.0,0.0]]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>18</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754013689789","contextId":"1-1-20-e6647cbcc0d94f33875606aaa13aaf19","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 10:04:16 | DEBUG | XML消息类型: 51
2025-08-01 10:04:16 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-01 10:04:16 | DEBUG | XML消息描述: None
2025-08-01 10:04:16 | DEBUG | 附件信息 totallen: 0
2025-08-01 10:04:16 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-01 10:04:16 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-01 10:04:16 | INFO | 未知的XML消息类型: 51
2025-08-01 10:04:16 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-01 10:04:16 | INFO | 消息描述: None
2025-08-01 10:04:16 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-01 10:04:16 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713407379855509650</objectId>
			<objectNonceId>14721346261057971314_4_20_13_1_1754013687666225_6f5b0b80-6e7b-11f0-a487-a957b245ab8c</objectNonceId>
			<feedType>4</feedType>
			<nickname>纯爱暴富和躺平</nickname>
			<username>v2_060000231003b20faec8cae0811ac7d4cf07ee3db0779ef103d8ea7691692af32226ab33f0ce@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/S3ibiaFJyq2ibpQNdSbPEdPSZGSRiaRS67Lmq2HhcDE3CRVuC30sFVcuexd8Lj4VhdTQiabLnnjv8vKSnY3xCuOjKKHtfgcB3qOJAlj8yaXFNkAGecKdPAPoVWPxdicqWAMicCF/0]]></avatar>
			<desc>好奇[发呆]法庭上真这样嘛
#搞笑#真的吗#八卦#好奇</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8ZnbQszuiaqNnMj3qZJrXJAIQ0N7Y8x0BXGWFica7JbGKcJsCFL7cGDqDoYu3eic4ytt130IP6ZdpXgg7HH7UicPqxXNam5jwttK7IQVAxcvh4wPls&hy=SZ&idx=1&m=f4f5020c674f0103082b4bcda720de49&uzid=7a206&token=6xykWLEnztJD1V02HxcJfcF9CUjFJDXfbNkjq5c11Yia65RUZTMDJLHAk1DCDH3eE0px3DqV57Le6f7icjFYko6P9bnOG1aPI8ZkkW1qibr25Gd2AX2IypsrCBLLTXPeA8RDjWgDicVKZ7D0ia3z017V0YT0KRpKcte94tz7Mlm0UXjk&basedata=CAESBnhXVDEyNhoGeFdUMTI2GgZ4V1QxMjcaBnhXVDExMRoGeFdUMTI4IgwKCgoGeFdUMTExEAEqBwi9HhAAGAI&sign=K2j9ViLnwan-s2S5eTJkigfDUrWf39zelrQx1v5jad5IQSU-60wVFiJgUmmkrPuxCb6N8XRDViyY12ZS54KvGg&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAABhVwxYNp5%2B26QK%2Bh%2BMaBAAAADnaHZTnGbFfAj9RgZXfw6VmVOk83whQgq7g498E%2FapmFSavOq4gkfJW%2FR7lId%2FXdOSrA1uIlarvYU%3D&svrnonce=1754013690]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Znxupt2jaibWrxAufSqUdVnljwlSGhoSZW7pHicYI2IbWFpp7xbmJ9476fuTmNcKW3EMNEkayicKibJsDBcg2bIlVfWB444DojuVbElgdicib3jHBEg&hy=SZ&idx=1&m=fbeac8323f87a00e2ea1e6032368d9fe&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eEyIrD6jL1CZCQybuGRJLqkBzKFia8gTibOjhb8RZWYClV9pgPpeyMEyESGA5kYDobDWaSIDticIp2VYnfmb69adfe4CK79dvl3kd7icicgg4piccapovzPr14zbZquB1wsD78s933rNveprH7d&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLIBn9G5YG8Znxupt2jaibWrxAufSqUdVnljwlSGhoSZW7pHicYI2IbWFpp7xbmJ9476fuTmNcKW3EMNEkayicKibJsDBcg2bIlVfWB444DojuVbElgdicib3jHBEg&hy=SZ&idx=1&m=fbeac8323f87a00e2ea1e6032368d9fe&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eEyIrD6jL1CZCQybuGRJLqkBzKFia8gTibOjhb8RZWYClV9pgPpeyMEyESGA5kYDobDWaSIDticIp2VYnfmb69adfe4CK79dvl3kd7icicgg4piccapovzPr14zbZquB1wsD78s933rNveprH7d&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[[0.0,0.0,0.0,0.0]]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>18</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754013689789","contextId":"1-1-20-e6647cbcc0d94f33875606aaa13aaf19","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 10:04:21 | DEBUG | 收到消息: {'MsgId': 496595287, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'Edison-w:\n我刚换电脑，原先有3.9和4.0，然后两个聊天记录都拷过来了，但是丢了很多聊天记录，只剩下24、25年的了。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013866, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Q3EXqLvG|v1_XTwwSZyJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\x7f\x7f\x7f\x7f麦来乱 : 我刚换电脑，原先有3.9和4.0，然后两个聊天记录都拷过来了，但是丢...', 'NewMsgId': 1190594609419965222, 'MsgSeq': 871416375}
2025-08-01 10:04:21 | INFO | 收到文本消息: 消息ID:496595287 来自:47325400669@chatroom 发送人:Edison-w @:[] 内容:我刚换电脑，原先有3.9和4.0，然后两个聊天记录都拷过来了，但是丢了很多聊天记录，只剩下24、25年的了。
2025-08-01 10:04:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我刚换电脑，原先有3.9和4.0，然后两个聊天记录都拷过来了，但是丢了很多聊天记录，只剩下24、25年的了。' from Edison-w in 47325400669@chatroom
2025-08-01 10:04:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['我刚换电脑，原先有3.9和4.0，然后两个聊天记录都拷过来了，但是丢了很多聊天记录，只剩下24、25年的了。']
2025-08-01 10:04:21 | DEBUG | 处理消息内容: '我刚换电脑，原先有3.9和4.0，然后两个聊天记录都拷过来了，但是丢了很多聊天记录，只剩下24、25年的了。'
2025-08-01 10:04:21 | DEBUG | 消息内容 '我刚换电脑，原先有3.9和4.0，然后两个聊天记录都拷过来了，但是丢了很多聊天记录，只剩下24、25年的了。' 不匹配任何命令，忽略
2025-08-01 10:04:47 | DEBUG | 收到消息: {'MsgId': 63154387, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'last--exile:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="cd1e9934d7ac241facc7a3291b2477e4" encryver="1" cdnthumbaeskey="cd1e9934d7ac241facc7a3291b2477e4" cdnthumburl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" cdnthumblength="4327" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" length="63267" md5="64c998ceb593e05f95b622b5b23b4931" hevc_mid_size="63267" originsourcemd5="d5b3b3421c088fb5cb9fea16035af50d">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013892, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>21a8b889df0f6099175344a152b511cd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_3tfCUywM|v1_No6UnwDt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一张图片', 'NewMsgId': 1911722274363922564, 'MsgSeq': 871416376}
2025-08-01 10:04:47 | INFO | 收到图片消息: 消息ID:63154387 来自:48097389945@chatroom 发送人:last--exile XML:<?xml version="1.0"?><msg><img aeskey="cd1e9934d7ac241facc7a3291b2477e4" encryver="1" cdnthumbaeskey="cd1e9934d7ac241facc7a3291b2477e4" cdnthumburl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" cdnthumblength="4327" cdnthumbheight="144" cdnthumbwidth="65" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20b0042438333939633162342d623265642d343039652d393266302d666430343035626461303663020405290a020201000405004c57c100" length="63267" md5="64c998ceb593e05f95b622b5b23b4931" hevc_mid_size="63267" originsourcemd5="d5b3b3421c088fb5cb9fea16035af50d"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 10:04:47 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 10:04:47 | INFO | [TimerTask] 缓存图片消息: 63154387
2025-08-01 10:04:56 | DEBUG | 收到消息: {'MsgId': 40093439, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'last--exile:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="32fe62afb6a91e40942a511ad9c2d951" encryver="1" cdnthumbaeskey="32fe62afb6a91e40942a511ad9c2d951" cdnthumburl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20cd042434663563383963652d363661352d346330652d613131382d353862623537613165656365020405250a020201000405004c4e6100" cdnthumblength="3803" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20cd042434663563383963652d363661352d346330652d613131382d353862623537613165656365020405250a020201000405004c4e6100" length="57756" md5="87757dfcbcff270c979dc10542acc6f6" originsourcemd5="5fb07639de3c661af8e26e6efb2d75b6">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013901, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>b6c8f321d37b87f114562a18fd335c99_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_PRHcHmNL|v1_aXiyXiis</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一张图片', 'NewMsgId': 3739056873807654743, 'MsgSeq': 871416377}
2025-08-01 10:04:56 | INFO | 收到图片消息: 消息ID:40093439 来自:48097389945@chatroom 发送人:last--exile XML:<?xml version="1.0"?><msg><img aeskey="32fe62afb6a91e40942a511ad9c2d951" encryver="1" cdnthumbaeskey="32fe62afb6a91e40942a511ad9c2d951" cdnthumburl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20cd042434663563383963652d363661352d346330652d613131382d353862623537613165656365020405250a020201000405004c4e6100" cdnthumblength="3803" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec6f9c1902032f8411020465ba587d0204688c20cd042434663563383963652d363661352d346330652d613131382d353862623537613165656365020405250a020201000405004c4e6100" length="57756" md5="87757dfcbcff270c979dc10542acc6f6" originsourcemd5="5fb07639de3c661af8e26e6efb2d75b6"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 10:04:56 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 10:04:56 | INFO | [TimerTask] 缓存图片消息: 40093439
2025-08-01 10:04:57 | DEBUG | 收到消息: {'MsgId': 1850975249, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'Edison-w:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>前面那个是3.9的，后面是4.0的</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>7361800843080652896</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>liubo-lamber</chatusr>\n\t\t\t<displayname>波波小白付费1W求大佬开发</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="ef9091877409aec6eced17fa6860eb41" encryver="1" cdnthumbaeskey="ef9091877409aec6eced17fa6860eb41" cdnthumburl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" cdnthumblength="3447" cdnthumbheight="101" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" length="42763" cdnbigimgurl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" hdlength="42763" md5="392967374fce6ab00aa58920badd4acc"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;794259524&lt;/sequence_id&gt;\n\t&lt;img_file_name&gt;3303f9b1-0a59-49cf-9a9c-2a714af3b6e7.png&lt;/img_file_name&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;1&lt;/fr&gt;\n\t\t&lt;cf&gt;3&lt;/cf&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;d1fadd5c9f84ed246a71eb07e290c27c_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="42763" cdnbigimgurl_size="42763" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_WuPoxYMm|v1_/GSgmBQ6&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753980277</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>Edison-w</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754013903, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>953dad9c12155ed4e12d0c21c518e7cf_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_iArFJUhj|v1_NZdxDyTj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\x7f\x7f\x7f\x7f麦来乱 : 前面那个是3.9的，后面是4.0的', 'NewMsgId': 7880027921404769588, 'MsgSeq': 871416378}
2025-08-01 10:04:57 | DEBUG | 从群聊消息中提取发送者: Edison-w
2025-08-01 10:04:57 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 10:04:57 | INFO | 收到引用消息: 消息ID:1850975249 来自:47325400669@chatroom 发送人:Edison-w 内容:前面那个是3.9的，后面是4.0的 引用类型:3
2025-08-01 10:04:57 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 10:04:57 | INFO | [DouBaoImageToImage] 消息内容: '前面那个是3.9的，后面是4.0的' from Edison-w in 47325400669@chatroom
2025-08-01 10:04:57 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['前面那个是3.9的，后面是4.0的']
2025-08-01 10:04:57 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 10:04:57 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 10:04:57 | INFO |   - 消息内容: 前面那个是3.9的，后面是4.0的
2025-08-01 10:04:57 | INFO |   - 群组ID: 47325400669@chatroom
2025-08-01 10:04:57 | INFO |   - 发送人: Edison-w
2025-08-01 10:04:57 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>前面那个是3.9的，后面是4.0的</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>7361800843080652896</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>liubo-lamber</chatusr>\n\t\t\t<displayname>波波小白付费1W求大佬开发</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="ef9091877409aec6eced17fa6860eb41" encryver="1" cdnthumbaeskey="ef9091877409aec6eced17fa6860eb41" cdnthumburl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" cdnthumblength="3447" cdnthumbheight="101" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" length="42763" cdnbigimgurl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" hdlength="42763" md5="392967374fce6ab00aa58920badd4acc"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;794259524&lt;/sequence_id&gt;\n\t&lt;img_file_name&gt;3303f9b1-0a59-49cf-9a9c-2a714af3b6e7.png&lt;/img_file_name&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;1&lt;/fr&gt;\n\t\t&lt;cf&gt;3&lt;/cf&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;d1fadd5c9f84ed246a71eb07e290c27c_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="42763" cdnbigimgurl_size="42763" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_WuPoxYMm|v1_/GSgmBQ6&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753980277</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>Edison-w</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '7361800843080652896', 'NewMsgId': '7361800843080652896', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': '波波小白付费1W求大佬开发', 'MsgSource': '<msgsource><sequence_id>794259524</sequence_id>\n\t<img_file_name>3303f9b1-0a59-49cf-9a9c-2a714af3b6e7.png</img_file_name>\n\t<alnode>\n\t\t<fr>1</fr>\n\t\t<cf>3</cf>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>d1fadd5c9f84ed246a71eb07e290c27c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="42763" cdnbigimgurl_size="42763" />\n\t<silence>1</silence>\n\t<membercount>222</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_WuPoxYMm|v1_/GSgmBQ6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753980277', 'SenderWxid': 'Edison-w'}
2025-08-01 10:04:57 | INFO |   - 引用消息ID: 
2025-08-01 10:04:57 | INFO |   - 引用消息类型: 
2025-08-01 10:04:57 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>前面那个是3.9的，后面是4.0的</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>7361800843080652896</svrid>
			<fromusr>47325400669@chatroom</fromusr>
			<chatusr>liubo-lamber</chatusr>
			<displayname>波波小白付费1W求大佬开发</displayname>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="ef9091877409aec6eced17fa6860eb41" encryver="1" cdnthumbaeskey="ef9091877409aec6eced17fa6860eb41" cdnthumburl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" cdnthumblength="3447" cdnthumbheight="101" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" length="42763" cdnbigimgurl="3057020100044b30490201000204c53d65ca02032fa73b0204b404e8780204688b9d74042433353539643862372d303831372d343364362d383130372d3031323531356537363133370204052838010201000405004c4e6300" hdlength="42763" md5="392967374fce6ab00aa58920badd4acc"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;794259524&lt;/sequence_id&gt;
	&lt;img_file_name&gt;3303f9b1-0a59-49cf-9a9c-2a714af3b6e7.png&lt;/img_file_name&gt;
	&lt;alnode&gt;
		&lt;fr&gt;1&lt;/fr&gt;
		&lt;cf&gt;3&lt;/cf&gt;
	&lt;/alnode&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;d1fadd5c9f84ed246a71eb07e290c27c_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="42763" cdnbigimgurl_size="42763" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;222&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_WuPoxYMm|v1_/GSgmBQ6&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1753980277</createtime>
		</refermsg>
	</appmsg>
	<fromusername>Edison-w</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 10:04:57 | INFO |   - 引用消息发送人: Edison-w
