2025-08-01 10:22:06 | SUCCESS | 读取主设置成功
2025-08-01 10:22:06 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 10:22:06 | INFO | 2025/08/01 10:22:06 GetRedisAddr: 127.0.0.1:6379
2025-08-01 10:22:06 | INFO | 2025/08/01 10:22:06 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 10:22:06 | INFO | 2025/08/01 10:22:06 Server start at :9000
2025-08-01 10:22:06 | SUCCESS | WechatAPI服务已启动
2025-08-01 10:22:07 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 10:22:07 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 10:22:07 | SUCCESS | 登录成功
2025-08-01 10:22:07 | SUCCESS | 已开启自动心跳
2025-08-01 10:22:07 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 10:22:07 | SUCCESS | 数据库初始化成功
2025-08-01 10:22:07 | SUCCESS | 定时任务已启动
2025-08-01 10:22:07 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 10:22:07 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:22:08 | INFO | 播客API初始化成功
2025-08-01 10:22:08 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 10:22:08 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 10:22:08 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 10:22:08 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 10:22:08 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 10:22:08 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 10:22:08 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 10:22:08 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 10:22:08 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 10:22:08 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 10:22:08 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 10:22:08 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 10:22:08 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 10:22:08 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 10:22:08 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 10:22:08 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 10:22:08 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 10:22:08 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 10:22:08 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 10:22:08 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 10:22:08 | DEBUG |   - 启用状态: True
2025-08-01 10:22:08 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 10:22:08 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 10:22:08 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 10:22:08 | DEBUG |   - Cookies配置: 已配置
2025-08-01 10:22:08 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 10:22:08 | DEBUG |   - 自然化响应: True
2025-08-01 10:22:08 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 10:22:08 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-01 10:22:08 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 10:22:08 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 10:22:08 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 10:22:08 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 10:22:08 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 10:22:08 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:22:08 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 10:22:08 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 10:22:08 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 10:22:08 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 10:22:08 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 10:22:08 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 10:22:08 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 10:22:08 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 10:22:09 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 10:22:09 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 10:22:09 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 10:22:09 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 10:22:09 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 10:22:09 | INFO | [yuanbao] 插件初始化完成
2025-08-01 10:22:09 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 10:22:09 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 10:22:09 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 10:22:09 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 10:22:09 | INFO | 处理堆积消息中
2025-08-01 10:22:10 | DEBUG | 接受到 1 条消息
2025-08-01 10:22:11 | SUCCESS | 处理堆积消息完毕
2025-08-01 10:22:11 | SUCCESS | 开始处理消息
2025-08-01 10:23:07 | DEBUG | 收到消息: {'MsgId': 1033989814, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n排位滴滴'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754014993, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_8Jcp5hLv|v1_xilBy9DS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6184304767276168799, 'MsgSeq': 871416414}
2025-08-01 10:23:07 | INFO | 收到文本消息: 消息ID:1033989814 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:排位滴滴
2025-08-01 10:23:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '排位滴滴' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-01 10:23:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['排位滴滴']
2025-08-01 10:23:07 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 10:23:07 | DEBUG | 处理消息内容: '排位滴滴'
2025-08-01 10:23:07 | DEBUG | 消息内容 '排位滴滴' 不匹配任何命令，忽略
2025-08-01 10:23:32 | DEBUG | 收到消息: {'MsgId': 1483579428, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="4000" length="11506" bufid="0" aeskey="34356f1f4152b5cd6ef6035d526cb077" voiceurl="3051020100044a304802010002035a663f02032f51490204e83122750204688c252a042433666431363266622d663236332d343930322d613261382d36366433343236353437346302040524000f0201000400" voicemd5="" clientmsgid="41386366366231333863396431366400371023080125d67e8f99015106" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754015018, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_UIeSrG3/|v1_BSg942Ph</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 2073241907706856387, 'MsgSeq': 871416415}
2025-08-01 10:23:32 | INFO | 收到语音消息: 消息ID:1483579428 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="4000" length="11506" bufid="0" aeskey="34356f1f4152b5cd6ef6035d526cb077" voiceurl="3051020100044a304802010002035a663f02032f51490204e83122750204688c252a042433666431363266622d663236332d343930322d613261382d36366433343236353437346302040524000f0201000400" voicemd5="" clientmsgid="41386366366231333863396431366400371023080125d67e8f99015106" fromusername="xiaomaochong" /></msg>
2025-08-01 10:24:31 | DEBUG | 收到消息: {'MsgId': 1146653567, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_mwil20w1e1j422:\n@小爱\u2005给我愤怒的素材'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754015076, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_6GVwcURA|v1_NToxxGqz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '37.2℃ : @小爱\u2005给我愤怒的素材', 'NewMsgId': 6890101837562214473, 'MsgSeq': 871416416}
2025-08-01 10:24:31 | INFO | 收到文本消息: 消息ID:1146653567 来自:48097389945@chatroom 发送人:wxid_mwil20w1e1j422 @:['xiaomaochong'] 内容:@小爱 给我愤怒的素材
2025-08-01 10:24:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@小爱 给我愤怒的素材' from wxid_mwil20w1e1j422 in 48097389945@chatroom
2025-08-01 10:24:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['@小爱\u2005给我愤怒的素材']
2025-08-01 10:24:31 | DEBUG | 处理消息内容: '@小爱 给我愤怒的素材'
2025-08-01 10:24:31 | DEBUG | 消息内容 '@小爱 给我愤怒的素材' 不匹配任何命令，忽略
2025-08-01 10:26:21 | DEBUG | 收到消息: {'MsgId': 1206588606, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n旺仔小乔哈哈\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754015186, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_iai+Q8aA|v1_y3DiqYEB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 旺仔小乔哈哈\n', 'NewMsgId': 1087743111738880526, 'MsgSeq': 871416417}
2025-08-01 10:26:21 | INFO | 收到文本消息: 消息ID:1206588606 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:[] 内容:旺仔小乔哈哈

2025-08-01 10:26:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '旺仔小乔哈哈' from wxid_laurnst5xn0q22 in 48097389945@chatroom
2025-08-01 10:26:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['旺仔小乔哈哈']
2025-08-01 10:26:21 | DEBUG | 处理消息内容: '旺仔小乔哈哈'
2025-08-01 10:26:21 | DEBUG | 消息内容 '旺仔小乔哈哈' 不匹配任何命令，忽略
2025-08-01 10:26:43 | DEBUG | 收到消息: {'MsgId': 1508279476, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n这样多开？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754015209, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_V1H9/dB0|v1_c+WJeaxV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 这样多开？', 'NewMsgId': 4311074685630635924, 'MsgSeq': 871416418}
2025-08-01 10:26:43 | INFO | 收到文本消息: 消息ID:1508279476 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:这样多开？
2025-08-01 10:26:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这样多开？' from seraph333 in 47325400669@chatroom
2025-08-01 10:26:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['这样多开？']
2025-08-01 10:26:43 | DEBUG | 处理消息内容: '这样多开？'
2025-08-01 10:26:43 | DEBUG | 消息内容 '这样多开？' 不匹配任何命令，忽略
2025-08-01 10:26:50 | DEBUG | 收到消息: {'MsgId': 862725199, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n不是有多开工具吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754015216, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_q3o1G4R1|v1_83xUnT3u</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 不是有多开工具吗', 'NewMsgId': 196835461834562591, 'MsgSeq': 871416419}
2025-08-01 10:26:50 | INFO | 收到文本消息: 消息ID:862725199 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:不是有多开工具吗
2025-08-01 10:26:50 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不是有多开工具吗' from seraph333 in 47325400669@chatroom
2025-08-01 10:26:50 | DEBUG | [DouBaoImageToImage] 命令解析: ['不是有多开工具吗']
2025-08-01 10:26:50 | DEBUG | 处理消息内容: '不是有多开工具吗'
2025-08-01 10:26:50 | DEBUG | 消息内容 '不是有多开工具吗' 不匹配任何命令，忽略
