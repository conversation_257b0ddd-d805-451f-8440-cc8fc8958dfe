2025-08-01 09:31:07 | SUCCESS | 读取主设置成功
2025-08-01 09:31:07 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 09:31:08 | INFO | 2025/08/01 09:31:07 GetRedisAddr: 127.0.0.1:6379
2025-08-01 09:31:08 | INFO | 2025/08/01 09:31:08 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 09:31:08 | INFO | 2025/08/01 09:31:08 Server start at :9000
2025-08-01 09:31:08 | SUCCESS | WechatAPI服务已启动
2025-08-01 09:31:09 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 09:31:09 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 09:31:09 | SUCCESS | 登录成功
2025-08-01 09:31:09 | SUCCESS | 已开启自动心跳
2025-08-01 09:31:09 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:31:09 | SUCCESS | 数据库初始化成功
2025-08-01 09:31:09 | SUCCESS | 定时任务已启动
2025-08-01 09:31:09 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 09:31:09 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:31:09 | INFO | 播客API初始化成功
2025-08-01 09:31:09 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:31:09 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 09:31:09 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 09:31:09 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 09:31:09 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 09:31:09 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 09:31:09 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 09:31:09 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 09:31:09 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 09:31:09 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 09:31:09 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 09:31:09 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 09:31:09 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 09:31:09 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 09:31:09 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 09:31:09 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 09:31:09 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 09:31:09 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 09:31:10 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 09:31:10 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 09:31:10 | DEBUG |   - 启用状态: True
2025-08-01 09:31:10 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 09:31:10 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 09:31:10 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 09:31:10 | DEBUG |   - Cookies配置: 已配置
2025-08-01 09:31:10 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 09:31:10 | DEBUG |   - 自然化响应: True
2025-08-01 09:31:10 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 09:31:10 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-01 09:31:10 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 09:31:10 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 09:31:10 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 09:31:10 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 09:31:10 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 09:31:10 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:31:10 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 09:31:10 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 09:31:10 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 09:31:10 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 09:31:10 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 09:31:10 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 09:31:10 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 09:31:10 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 09:31:11 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 09:31:11 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 09:31:11 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 09:31:11 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 09:31:11 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 09:31:11 | INFO | [yuanbao] 插件初始化完成
2025-08-01 09:31:11 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 09:31:11 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 09:31:11 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 09:31:11 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 09:31:11 | INFO | 处理堆积消息中
2025-08-01 09:31:12 | SUCCESS | 处理堆积消息完毕
2025-08-01 09:31:12 | SUCCESS | 开始处理消息
2025-08-01 09:31:13 | DEBUG | 收到消息: {'MsgId': 857833176, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n这群里最真的就是小爱和我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011878, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>12</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_YiFFy/6K|v1_i/zN75gj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 这群里最真的就是小爱和我', 'NewMsgId': 2001260582613461981, 'MsgSeq': 871416319}
2025-08-01 09:31:13 | INFO | 收到文本消息: 消息ID:857833176 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:这群里最真的就是小爱和我
2025-08-01 09:31:14 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这群里最真的就是小爱和我' from wxid_jegyk4i3v7zg22 in 48097389945@chatroom
2025-08-01 09:31:14 | DEBUG | [DouBaoImageToImage] 命令解析: ['这群里最真的就是小爱和我']
2025-08-01 09:31:14 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 09:31:14 | DEBUG | 处理消息内容: '这群里最真的就是小爱和我'
2025-08-01 09:31:14 | DEBUG | 消息内容 '这群里最真的就是小爱和我' 不匹配任何命令，忽略
2025-08-01 09:31:30 | DEBUG | 收到消息: {'MsgId': 1413512109, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011896, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_ilcabb7z|v1_7iqnXfS5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5530308330592271432, 'MsgSeq': 871416320}
2025-08-01 09:31:30 | INFO | 收到表情消息: 消息ID:1413512109 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[抠鼻]
2025-08-01 09:31:30 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5530308330592271432
2025-08-01 09:31:46 | DEBUG | 收到消息: {'MsgId': 1267521216, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 换不同姿势</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>3039325562699437542</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;684415c9ca831ee2b5db0b9a8f9c34f7_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_9esKwq3j|v1_F1cA5tA5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="7177796a7167666b707a796d6e6a6f77" encryver="0" cdnthumbaeskey="7177796a7167666b707a796d6e6a6f77" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" cdnthumblength="3558" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" length="85864" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" hdlength="1487367" md5="f667f99f7e210556370f35f0560c2296"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754007194</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754011911, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>684415c9ca831ee2b5db0b9a8f9c34f7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_8GqdOIF4|v1_HY3Cb4LT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 豆包 换不同姿势', 'NewMsgId': 9001359398265106800, 'MsgSeq': 871416321}
2025-08-01 09:31:46 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-01 09:31:46 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:31:46 | INFO | 收到引用消息: 消息ID:1267521216 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:豆包 换不同姿势 引用类型:3
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage] 消息内容: '豆包 换不同姿势' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-01 09:31:46 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['豆包', '换不同姿势']
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage] 识别到图生图引用命令: 豆包
2025-08-01 09:31:46 | DEBUG | [DouBaoImageToImage] 解析到提示词: '换不同姿势'
2025-08-01 09:31:46 | DEBUG | [DouBaoImageToImage] 开始解析提示词、风格和比例...
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage] 引用图片图生图参数:
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage]   - 原始提示词: '换不同姿势'
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage]   - 解析后提示词: '换不同姿势，比例「2:3」'
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage]   - 比例: 832x1248 (2:3)
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage]   - 风格: None
2025-08-01 09:31:46 | DEBUG | [DouBaoImageToImage] 开始检查用户限流...
2025-08-01 09:31:46 | DEBUG | [DouBaoImageToImage] 检查用户限流状态: wxid_ubbh6q832tcs21
2025-08-01 09:31:46 | DEBUG | [DouBaoImageToImage] 当前时间: 1754011906.8852897, 冷却时间: 15秒
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage] 新用户 wxid_ubbh6q832tcs21，允许请求
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage] 开始处理引用的图片...
2025-08-01 09:31:46 | INFO | [DouBaoImageToImage] 被引用消息类型: 3
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 成功下载引用的图片并保存到: temp\doubao_image_to_image\quoted_image_1754011907.jpg
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 开始豆包AI处理流程(无通知)，用户: wxid_ubbh6q832tcs21, 提示词: 换不同姿势，比例「2:3」, 比例: 832x1248
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 步骤1: 验证图片文件...
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 图片验证成功，大小: 83.9KB
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 步骤2: 调用豆包AI接口...
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] ========== 开始豆包AI图生图处理 ==========
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 输入参数:
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage]   - 提示词: '换不同姿势，比例「2:3」'
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage]   - 图片路径: temp\doubao_image_to_image\quoted_image_1754011907.jpg
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage]   - 比例配置: 2:3 (832x1248)
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage]   - 风格值: None
2025-08-01 09:31:47 | DEBUG | [DouBaoImageToImage] Cookie配置检查通过，长度: 2200
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 图片文件验证通过，大小: 83.9KB
2025-08-01 09:31:47 | DEBUG | [DouBaoImageToImage] 创建豆包AI生成器实例...
2025-08-01 09:31:47 | INFO | [DouBaoImageToImage] 开始调用豆包AI处理流程...
2025-08-01 09:31:47 | INFO | [DoubaoImageGenerator] 开始处理图片流程 - 路径: temp\doubao_image_to_image\quoted_image_1754011907.jpg, 提示词: '换不同姿势，比例「2:3」', 风格: None
2025-08-01 09:31:47 | DEBUG | [DoubaoImageGenerator] 创建HTTP客户端成功，超时设置: 300秒
2025-08-01 09:31:47 | INFO | [DoubaoImageGenerator] 步骤1: 上传图片...
2025-08-01 09:31:47 | DEBUG | [DoubaoImageGenerator] 开始上传图片: temp\doubao_image_to_image\quoted_image_1754011907.jpg
2025-08-01 09:31:47 | INFO | [DoubaoImageGenerator] 图片信息 - 大小: 83.9KB, 扩展名: .jpg, CRC32: a0bd4c69
2025-08-01 09:31:47 | DEBUG | [DoubaoImageGenerator] 开始获取上传认证...
2025-08-01 09:31:47 | DEBUG | [DoubaoImageGenerator] 请求上传认证 URL: https://www.doubao.com/alice/resource/prepare_upload
2025-08-01 09:31:47 | DEBUG | [DoubaoImageGenerator] 请求参数: {'version_code': '20800', 'language': 'zh', 'device_platform': 'web', 'aid': '497858', 'real_aid': '497858', 'pkg_type': 'release_version', 'device_id': '7468716989062841895', 'web_id': '7468716986638386703', 'tea_uuid': '7468716986638386703', 'use-olympus-account': '1', 'region': 'CN', 'sys_region': 'CN', 'samantha_web': '1', 'pc_version': '2.24.2'}
2025-08-01 09:31:47 | DEBUG | [DoubaoImageGenerator] 请求数据: {'tenant_id': '5', 'scene_id': '5', 'resource_type': 2}
2025-08-01 09:31:49 | DEBUG | [DoubaoImageGenerator] 上传认证响应状态: 200
2025-08-01 09:31:49 | DEBUG | [DoubaoImageGenerator] 上传认证响应: {'code': 0, 'msg': '', 'data': {'service_id': 'a9rns2rl98', 'upload_path_prefix': 'rc/pc/bot-chat', 'upload_host': 'imagex.bytedanceapi.com', 'upload_auth_token': {'access_key': 'AKTPN2FmZjZjY2Y2YTRmNDg3MWEzYzMwYTAyNGZjOTVmZGI', 'secret_key': 'IAAJ7oHMaWpCkIxjvyl9ixLI2OT0Ip3v4AMkDePTBidKq5/ulJLVuiMKRIRYp95C', 'session_token': 'STS2eyJMVEFjY2Vzc0tleUlkIjoiQUtMVFlUZGhPR0ptWVRNNFl6ZG1OR1JoWVRoaE0yWTJPVFl5TW1SbU0yRmhNREEiLCJBY2Nlc3NLZXlJZCI6IkFLVFBOMkZtWmpaalkyWTJZVFJtTkRnM01XRXpZek13WVRBeU5HWmpPVFZtWkdJIiwiU2lnbmVkU2VjcmV0QWNjZXNzS2V5IjoiYi9tVFMyN01mRGI1dTlITU8wNUcyVDhFcU9qbzJkbmZrNFUvajdzQWRCWTF3U3lYU3l2YThRajRJaTdlaW93anBkNlNwMVludlAvWlJMcExiVUVWV09VUHp4M210ME42VkxOdlVuSjFTWnM9IiwiRXhwaXJlZFRpbWUiOjE3NTQwMTU1MTUsIlBvbGljeVN0cmluZyI6IntcIlN0YXRlbWVudFwiOlt7XCJFZmZlY3RcIjpcIkFsbG93XCIsXCJBY3Rpb25cIjpbXCJJbWFnZVg6QXBwbHlJbWFnZVVwbG9hZFwiLFwiSW1hZ2VYOkNvbW1pdEltYWdlVXBsb2FkXCJdLFwiUmVzb3VyY2VcIjpbXCJ0cm46SW1hZ2VYOio6KjpTZXJ2aWNlSWQvYTlybnMycmw5OFwiXX0se1wiRWZmZWN0XCI6XCJBbGxvd1wiLFwiQWN0aW9uXCI6W1wiUFNNXCJdLFwiUmVzb3VyY2VcIjpbXCJmbG93LmFsaWNlLnJlc291cmNlX2NlbnRlclwiXX1dfSIsIlNpZ25hdHVyZSI6ImU5NWZiOTVmOTI4YzEzZDYzMWMzYmNjMmY5NGM1ODhkMDk2M2I1ZmRkYWU3NWZhYWNhYTI1Nzk5NzM1ZjVhNGMifQ==', 'expired_time': '2025-08-01T10:31:55+08:00', 'current_time': '2025-08-01T09:31:55+08:00'}}}
2025-08-01 09:31:49 | INFO | [DoubaoImageGenerator] 成功获取上传认证，token长度: 1122
2025-08-01 09:31:49 | DEBUG | [DoubaoImageGenerator] 申请上传 URL: https://imagex.bytedanceapi.com/?Action=ApplyImageUpload&Version=2018-08-01&ServiceId=a9rns2rl98&NeedFallback=true&FileSize=85864&FileExtension=.jpg&s=yy49d6n7o6p
2025-08-01 09:31:49 | DEBUG | [DoubaoImageGenerator] 申请上传请求头: {'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'Accept': '*/*', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Accept-Encoding': 'gzip, deflate', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/create-image', 'X-Requested-With': 'mark.via', 'Authorization': 'AWS4-HMAC-SHA256 Credential=AKTPN2FmZjZjY2Y2YTRmNDg3MWEzYzMwYTAyNGZjOTVmZGI/20250801/cn-north-1/imagex/aws4_request, SignedHeaders=host;x-amz-date;x-amz-security-token, Signature=42dac34e17bf5563d0840185a22e1494869ef89f9234ddbc803736fa6142afcc', 'X-Amz-Date': '20250801T013149Z', 'x-amz-security-token': 'STS2eyJMVEFjY2Vzc0tleUlkIjoiQUtMVFlUZGhPR0ptWVRNNFl6ZG1OR1JoWVRoaE0yWTJPVFl5TW1SbU0yRmhNREEiLCJBY2Nlc3NLZXlJZCI6IkFLVFBOMkZtWmpaalkyWTJZVFJtTkRnM01XRXpZek13WVRBeU5HWmpPVFZtWkdJIiwiU2lnbmVkU2VjcmV0QWNjZXNzS2V5IjoiYi9tVFMyN01mRGI1dTlITU8wNUcyVDhFcU9qbzJkbmZrNFUvajdzQWRCWTF3U3lYU3l2YThRajRJaTdlaW93anBkNlNwMVludlAvWlJMcExiVUVWV09VUHp4M210ME42VkxOdlVuSjFTWnM9IiwiRXhwaXJlZFRpbWUiOjE3NTQwMTU1MTUsIlBvbGljeVN0cmluZyI6IntcIlN0YXRlbWVudFwiOlt7XCJFZmZlY3RcIjpcIkFsbG93XCIsXCJBY3Rpb25cIjpbXCJJbWFnZVg6QXBwbHlJbWFnZVVwbG9hZFwiLFwiSW1hZ2VYOkNvbW1pdEltYWdlVXBsb2FkXCJdLFwiUmVzb3VyY2VcIjpbXCJ0cm46SW1hZ2VYOio6KjpTZXJ2aWNlSWQvYTlybnMycmw5OFwiXX0se1wiRWZmZWN0XCI6XCJBbGxvd1wiLFwiQWN0aW9uXCI6W1wiUFNNXCJdLFwiUmVzb3VyY2VcIjpbXCJmbG93LmFsaWNlLnJlc291cmNlX2NlbnRlclwiXX1dfSIsIlNpZ25hdHVyZSI6ImU5NWZiOTVmOTI4YzEzZDYzMWMzYmNjMmY5NGM1ODhkMDk2M2I1ZmRkYWU3NWZhYWNhYTI1Nzk5NzM1ZjVhNGMifQ=='}
2025-08-01 09:31:50 | DEBUG | [DoubaoImageGenerator] 申请上传响应状态: 200
2025-08-01 09:31:50 | DEBUG | [DoubaoImageGenerator] 申请上传成功: {'UploadAddress': {'StoreInfos': [{'StoreUri': 'tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg', 'Auth': 'SpaceKey/a9rns2rl98/1/:version:v2:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.u7DeBvLTBP-cX5hnWOQoaNpVET4Y8AINR6O6CzSlUXI', 'UploadID': 'c4dfb3f5986d4255afcbaecd2304902c'}], 'UploadHosts': ['tos-d-ct-lf.snssdk.com'], 'UploadHeader': None, 'SessionKey': 'eyJhY2NvdW50VHlwZSI6IkltYWdlWCIsImFwcElkIjoiIiwiYml6VHlwZSI6IiIsImZpbGVUeXBlIjoiaW1hZ2UiLCJsZWdhbCI6IiIsInN0b3JlSW5mb3MiOiJbe1wiU3RvcmVVcmlcIjpcInRvcy1jbi1pLWE5cm5zMnJsOTgvZmMxZjU1YTk5M2Q0NDYyZjgzYTMwNTFlMGM1M2MzODUuanBnXCIsXCJBdXRoXCI6XCJTcGFjZUtleS9hOXJuczJybDk4LzEvOnZlcnNpb246djI6ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmxlSEFpT2pFM05UUXdNek0xTVRZc0luTnBaMjVoZEhWeVpVbHVabThpT25zaVlXTmpaWE56UzJWNUlqb2labUZyWlY5aFkyTmxjM05mYTJWNUlpd2lZblZqYTJWMElqb2lkRzl6TFdOdUxXa3RZVGx5Ym5NeWNtdzVPQ0lzSW1WNGNHbHlaU0k2TVRjMU5EQXpNelV4Tml3aVptbHNaVWx1Wm05eklqcGJleUp2YVdSTFpYa2lPaUptWXpGbU5UVmhPVGt6WkRRME5qSm1PRE5oTXpBMU1XVXdZelV6WXpNNE5TNXFjR2NpTENKbWFXeGxWSGx3WlNJNklqRWlmVjBzSW1WNGRISmhJanA3SW1GalkyOTFiblJmY0hKdlpIVmpkQ0k2SW1sdFlXZGxlQ0lzSW1Kc2IyTnJYMjF2WkdVaU9pSWlMQ0pqYjI1MFpXNTBYM1I1Y0dWZllteHZZMnNpT2lKN1hDSnRhVzFsWDNCamRGd2lPakFzWENKdGIyUmxYQ0k2TUN4Y0ltMXBiV1ZmYkdsemRGd2lPbTUxYkd3c1hDSmpiMjVtYkdsamRGOWliRzlqYTF3aU9tWmhiSE5sZlNJc0ltVnVZM0o1Y0hSZllXeG5ieUk2SWlJc0ltVnVZM0o1Y0hSZmEyVjVJam9pSWl3aVpYaDBYMk52Ym5SbGJuUmZkSGx3WlNJNkltbHRZV2RsTDJwd1pXY2lMQ0pwYzE5cGJXRm5aWGdpT25SeWRXVXNJbk53WVdObElqb2lZVGx5Ym5NeWNtdzVPQ0o5ZlgwLnU3RGVCdkxUQlAtY1g1aG5XT1FvYU5wVkVUNFk4QUlOUjZPNkN6U2xVWElcIixcIlVwbG9hZElEXCI6XCJjNGRmYjNmNTk4NmQ0MjU1YWZjYmFlY2QyMzA0OTAyY1wiLFwiVXBsb2FkSGVhZGVyXCI6bnVsbCxcIlN0b3JhZ2VIZWFkZXJcIjpudWxsfV0iLCJ1cGxvYWRIb3N0IjoidG9zLWQtY3QtbGYuc25zc2RrLmNvbSIsInVyaSI6InRvcy1jbi1pLWE5cm5zMnJsOTgvZmMxZjU1YTk5M2Q0NDYyZjgzYTMwNTFlMGM1M2MzODUuanBnIiwidXNlcklkIjoiIn0=', 'Cloud': ''}, 'FallbackUploadAddress': {'StoreInfos': None, 'UploadHosts': None, 'UploadHeader': None, 'SessionKey': '', 'Cloud': ''}, 'InnerUploadAddress': {'UploadNodes': [{'StoreInfos': [{'StoreUri': 'tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg', 'Auth': 'SpaceKey/a9rns2rl98/1/:version:v2:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.u7DeBvLTBP-cX5hnWOQoaNpVET4Y8AINR6O6CzSlUXI', 'UploadID': 'c4dfb3f5986d4255afcbaecd2304902c'}], 'UploadHost': 'tos-d-ct-lf.snssdk.com', 'UploadHeader': None, 'SessionKey': 'eyJhY2NvdW50VHlwZSI6IkltYWdlWCIsImFwcElkIjoiIiwiYml6VHlwZSI6IiIsImZpbGVUeXBlIjoiaW1hZ2UiLCJsZWdhbCI6IiIsInN0b3JlSW5mb3MiOiJbe1wiU3RvcmVVcmlcIjpcInRvcy1jbi1pLWE5cm5zMnJsOTgvZmMxZjU1YTk5M2Q0NDYyZjgzYTMwNTFlMGM1M2MzODUuanBnXCIsXCJBdXRoXCI6XCJTcGFjZUtleS9hOXJuczJybDk4LzEvOnZlcnNpb246djI6ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmxlSEFpT2pFM05UUXdNek0xTVRZc0luTnBaMjVoZEhWeVpVbHVabThpT25zaVlXTmpaWE56UzJWNUlqb2labUZyWlY5aFkyTmxjM05mYTJWNUlpd2lZblZqYTJWMElqb2lkRzl6TFdOdUxXa3RZVGx5Ym5NeWNtdzVPQ0lzSW1WNGNHbHlaU0k2TVRjMU5EQXpNelV4Tml3aVptbHNaVWx1Wm05eklqcGJleUp2YVdSTFpYa2lPaUptWXpGbU5UVmhPVGt6WkRRME5qSm1PRE5oTXpBMU1XVXdZelV6WXpNNE5TNXFjR2NpTENKbWFXeGxWSGx3WlNJNklqRWlmVjBzSW1WNGRISmhJanA3SW1GalkyOTFiblJmY0hKdlpIVmpkQ0k2SW1sdFlXZGxlQ0lzSW1Kc2IyTnJYMjF2WkdVaU9pSWlMQ0pqYjI1MFpXNTBYM1I1Y0dWZllteHZZMnNpT2lKN1hDSnRhVzFsWDNCamRGd2lPakFzWENKdGIyUmxYQ0k2TUN4Y0ltMXBiV1ZmYkdsemRGd2lPbTUxYkd3c1hDSmpiMjVtYkdsamRGOWliRzlqYTF3aU9tWmhiSE5sZlNJc0ltVnVZM0o1Y0hSZllXeG5ieUk2SWlJc0ltVnVZM0o1Y0hSZmEyVjVJam9pSWl3aVpYaDBYMk52Ym5SbGJuUmZkSGx3WlNJNkltbHRZV2RsTDJwd1pXY2lMQ0pwYzE5cGJXRm5aWGdpT25SeWRXVXNJbk53WVdObElqb2lZVGx5Ym5NeWNtdzVPQ0o5ZlgwLnU3RGVCdkxUQlAtY1g1aG5XT1FvYU5wVkVUNFk4QUlOUjZPNkN6U2xVWElcIixcIlVwbG9hZElEXCI6XCJjNGRmYjNmNTk4NmQ0MjU1YWZjYmFlY2QyMzA0OTAyY1wiLFwiVXBsb2FkSGVhZGVyXCI6bnVsbCxcIlN0b3JhZ2VIZWFkZXJcIjpudWxsfV0iLCJ1cGxvYWRIb3N0IjoidG9zLWQtY3QtbGYuc25zc2RrLmNvbSIsInVyaSI6InRvcy1jbi1pLWE5cm5zMnJsOTgvZmMxZjU1YTk5M2Q0NDYyZjgzYTMwNTFlMGM1M2MzODUuanBnIiwidXNlcklkIjoiIn0='}]}, 'RequestId': '202508010931569306521D42A0CF2E55A9', 'SDKParam': None}
2025-08-01 09:31:50 | INFO | [DoubaoImageGenerator] 开始上传到: https://tos-d-ct-lf.snssdk.com/upload/v1/tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg
2025-08-01 09:31:50 | DEBUG | [DoubaoImageGenerator] 上传请求头: {'Authorization': 'SpaceKey/a9rns2rl98/1/:version:v2:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.u7DeBvLTBP-cX5hnWOQoaNpVET4Y8AINR6O6CzSlUXI', 'Content-CRC32': 'a0bd4c69', 'Content-Type': 'application/octet-stream', 'X-Storage-U': 'c4dfb3f5986d4255afcbaecd2304902c', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 上传响应状态: 200
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 上传响应: {'code': 2000, 'apiversion': 'v1', 'message': 'Success', 'data': {'crc32': 'a0bd4c69'}}
2025-08-01 09:31:51 | INFO | [DoubaoImageGenerator] 图片上传成功
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 提交上传 URL: https://imagex.bytedanceapi.com/?Action=CommitImageUpload&Version=2018-08-01&ServiceId=a9rns2rl98
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 提交载荷: {"SessionKey": "eyJhY2NvdW50VHlwZSI6IkltYWdlWCIsImFwcElkIjoiIiwiYml6VHlwZSI6IiIsImZpbGVUeXBlIjoiaW1hZ2UiLCJsZWdhbCI6IiIsInN0b3JlSW5mb3MiOiJbe1wiU3RvcmVVcmlcIjpcInRvcy1jbi1pLWE5cm5zMnJsOTgvZmMxZjU1YTk5M2Q0NDYyZjgzYTMwNTFlMGM1M2MzODUuanBnXCIsXCJBdXRoXCI6XCJTcGFjZUtleS9hOXJuczJybDk4LzEvOnZlcnNpb246djI6ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmxlSEFpT2pFM05UUXdNek0xTVRZc0luTnBaMjVoZEhWeVpVbHVabThpT25zaVlXTmpaWE56UzJWNUlqb2labUZyWlY5aFkyTmxjM05mYTJWNUlpd2lZblZqYTJWMElqb2lkRzl6TFdOdUxXa3RZVGx5Ym5NeWNtdzVPQ0lzSW1WNGNHbHlaU0k2TVRjMU5EQXpNelV4Tml3aVptbHNaVWx1Wm05eklqcGJleUp2YVdSTFpYa2lPaUptWXpGbU5UVmhPVGt6WkRRME5qSm1PRE5oTXpBMU1XVXdZelV6WXpNNE5TNXFjR2NpTENKbWFXeGxWSGx3WlNJNklqRWlmVjBzSW1WNGRISmhJanA3SW1GalkyOTFiblJmY0hKdlpIVmpkQ0k2SW1sdFlXZGxlQ0lzSW1Kc2IyTnJYMjF2WkdVaU9pSWlMQ0pqYjI1MFpXNTBYM1I1Y0dWZllteHZZMnNpT2lKN1hDSnRhVzFsWDNCamRGd2lPakFzWENKdGIyUmxYQ0k2TUN4Y0ltMXBiV1ZmYkdsemRGd2lPbTUxYkd3c1hDSmpiMjVtYkdsamRGOWliRzlqYTF3aU9tWmhiSE5sZlNJc0ltVnVZM0o1Y0hSZllXeG5ieUk2SWlJc0ltVnVZM0o1Y0hSZmEyVjVJam9pSWl3aVpYaDBYMk52Ym5SbGJuUmZkSGx3WlNJNkltbHRZV2RsTDJwd1pXY2lMQ0pwYzE5cGJXRm5aWGdpT25SeWRXVXNJbk53WVdObElqb2lZVGx5Ym5NeWNtdzVPQ0o5ZlgwLnU3RGVCdkxUQlAtY1g1aG5XT1FvYU5wVkVUNFk4QUlOUjZPNkN6U2xVWElcIixcIlVwbG9hZElEXCI6XCJjNGRmYjNmNTk4NmQ0MjU1YWZjYmFlY2QyMzA0OTAyY1wiLFwiVXBsb2FkSGVhZGVyXCI6bnVsbCxcIlN0b3JhZ2VIZWFkZXJcIjpudWxsfV0iLCJ1cGxvYWRIb3N0IjoidG9zLWQtY3QtbGYuc25zc2RrLmNvbSIsInVyaSI6InRvcy1jbi1pLWE5cm5zMnJsOTgvZmMxZjU1YTk5M2Q0NDYyZjgzYTMwNTFlMGM1M2MzODUuanBnIiwidXNlcklkIjoiIn0="}
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 提交请求头: {'Content-Type': 'application/json', 'Accept': '*/*', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'Authorization': 'AWS4-HMAC-SHA256 Credential=AKTPN2FmZjZjY2Y2YTRmNDg3MWEzYzMwYTAyNGZjOTVmZGI/20250801/cn-north-1/imagex/aws4_request, SignedHeaders=host;x-amz-date;x-amz-security-token, Signature=e0ade223808979a16cf2f6469b7e49eaba15185b5aff5aef731108eadb07e4ca', 'X-Amz-Date': '20250801T013151Z', 'x-amz-security-token': 'STS2eyJMVEFjY2Vzc0tleUlkIjoiQUtMVFlUZGhPR0ptWVRNNFl6ZG1OR1JoWVRoaE0yWTJPVFl5TW1SbU0yRmhNREEiLCJBY2Nlc3NLZXlJZCI6IkFLVFBOMkZtWmpaalkyWTJZVFJtTkRnM01XRXpZek13WVRBeU5HWmpPVFZtWkdJIiwiU2lnbmVkU2VjcmV0QWNjZXNzS2V5IjoiYi9tVFMyN01mRGI1dTlITU8wNUcyVDhFcU9qbzJkbmZrNFUvajdzQWRCWTF3U3lYU3l2YThRajRJaTdlaW93anBkNlNwMVludlAvWlJMcExiVUVWV09VUHp4M210ME42VkxOdlVuSjFTWnM9IiwiRXhwaXJlZFRpbWUiOjE3NTQwMTU1MTUsIlBvbGljeVN0cmluZyI6IntcIlN0YXRlbWVudFwiOlt7XCJFZmZlY3RcIjpcIkFsbG93XCIsXCJBY3Rpb25cIjpbXCJJbWFnZVg6QXBwbHlJbWFnZVVwbG9hZFwiLFwiSW1hZ2VYOkNvbW1pdEltYWdlVXBsb2FkXCJdLFwiUmVzb3VyY2VcIjpbXCJ0cm46SW1hZ2VYOio6KjpTZXJ2aWNlSWQvYTlybnMycmw5OFwiXX0se1wiRWZmZWN0XCI6XCJBbGxvd1wiLFwiQWN0aW9uXCI6W1wiUFNNXCJdLFwiUmVzb3VyY2VcIjpbXCJmbG93LmFsaWNlLnJlc291cmNlX2NlbnRlclwiXX1dfSIsIlNpZ25hdHVyZSI6ImU5NWZiOTVmOTI4YzEzZDYzMWMzYmNjMmY5NGM1ODhkMDk2M2I1ZmRkYWU3NWZhYWNhYTI1Nzk5NzM1ZjVhNGMifQ=='}
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 提交响应状态: 200
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 提交响应: {'ResponseMetadata': {'RequestId': '20250801093157E5A81A4F4956B76599A7', 'Action': 'CommitImageUpload', 'Version': '2018-08-01', 'Service': 'imagex', 'Region': 'cn-north-1'}, 'Result': {'Results': [{'Uri': 'tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg', 'UriStatus': 2000}], 'RequestId': '20250801093157E5A81A4F4956B76599A7', 'PluginResult': [{'FileName': 'fc1f55a993d4462f83a3051e0c53c385.jpg', 'SourceUri': 'tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg', 'ImageUri': 'tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg', 'ImageWidth': 720, 'ImageHeight': 1280, 'ImageMd5': '1ed45ba2db54f109397516aa7637b502', 'ImageFormat': 'jpeg', 'ImageSize': 85864, 'FrameCnt': 1}]}}
2025-08-01 09:31:51 | INFO | [DoubaoImageGenerator] 图片上传完成，URI: tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg
2025-08-01 09:31:51 | INFO | [DoubaoImageGenerator] 图片上传成功，URI: tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg
2025-08-01 09:31:51 | INFO | [DoubaoImageGenerator] 步骤2: 生成图片...
2025-08-01 09:31:51 | INFO | [DoubaoImageGenerator] 开始生成图片 - 提示词: '换不同姿势，比例「2:3」', 图片URI: tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg, 风格: None
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 请求数据: {
  "messages": [
    {
      "content": "{\"text\": \"\\u6362\\u4e0d\\u540c\\u59ff\\u52bf\\uff0c\\u6bd4\\u4f8b\\u300c2:3\\u300d\\uff01\"}",
      "content_type": 2009,
      "attachments": [
        {
          "type": "image",
          "key": "tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg",
          "extra": {
            "refer_types": "overall"
          },
          "identifier": "8706b219-d5be-49fc-8d70-f5d6fa43839d"
        }
      ],
      "references": []
    }
  ],
  "completion_option": {
    "is_regen": false,
    "with_suggest": false,
    "need_create_conversation": true,
    "launch_stage": 1,
    "is_replace": false,
    "is_delete": false,
    "message_from": 0,
    "use_auto_cot": false,
    "resend_for_regen": false,
    "event_id": "0"
  },
  "conversation_id": "0",
  "local_conversation_id": "local_1754011911301049",
  "local_message_id": "d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f"
}
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 请求头: {'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Accept-Encoding': 'gzip, deflate', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/create-image', 'X-Requested-With': 'mark.via', 'Content-Type': 'application/json', 'x-flow-trace': '04-b2531b8c572d42c2-88c42eb982384b67-01', 'Agw-Js-Conv': 'str', 'last-event-id': 'undefined', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty'}
2025-08-01 09:31:51 | DEBUG | [DoubaoImageGenerator] 请求URL: https://www.doubao.com/samantha/chat/completion?version_code=20800&language=zh&device_platform=web&aid=497858&real_aid=497858&pkg_type=release_version&device_id=7468716989062841895&web_id=7468716986638386703&tea_uuid=7468716986638386703&use-olympus-account=1&region=CN&sys_region=CN&samantha_web=1&pc_version=2.24.2
2025-08-01 09:31:54 | DEBUG | [DoubaoImageGenerator] 流式响应状态: 200
2025-08-01 09:31:54 | INFO | [DoubaoImageGenerator] 开始处理流式响应...
2025-08-01 09:31:54 | DEBUG | [DoubaoImageGenerator] 第1行事件数据: {'event_data': '{"message_id":"*****************","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","message_index":1,"conversation_type":5}', 'event_id': '0', 'event_type': 2002}
2025-08-01 09:31:54 | DEBUG | [DoubaoImageGenerator] 第3行事件数据: {'event_data': '{"proto_version":2,"message_id":"*****************","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","message_index":1,"conversation_type":5}', 'event_id': '1', 'event_type': 2011}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 第5行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"好\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '2', 'event_type': 2001}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"好"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 第7行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '3', 'event_type': 2001}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 第9行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '4', 'event_type': 2001}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 第11行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"我\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '5', 'event_type': 2001}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"我"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 第13行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"将\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '6', 'event_type': 2001}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"将"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 第15行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '7', 'event_type': 2001}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为
2025-08-01 09:31:57 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第17行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"你\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '8', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"你"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第19行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"上传\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '9', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"上传"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第21行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"的\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '10', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"的"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第23行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"图片\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '11', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"图片"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第25行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"中的\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '12', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"中的"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第27行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"人物\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '13', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"人物"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第29行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"更换\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '14', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"更换"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第31行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"不同\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '15', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"不同"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第33行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"姿势\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '16', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"姿势"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第35行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"，并\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '17', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"，并"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第37行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"将\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '18', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"将"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第39行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"比例\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '19', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"比例"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第41行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"调整\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '20', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"调整"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第43行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"为\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '21', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"为"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第45行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"2\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '22', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"2"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第47行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\":\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '23', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":":"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第49行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"3\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '24', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"3"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 第51行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{\\"text\\":\\"。\\"}","id":"48f5505b-dcd7-4afd-b606-bd2b1296808c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '25', 'event_type': 2001}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{"text":"。"}', 'id': '48f5505b-dcd7-4afd-b606-bd2b1296808c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 累积文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。
2025-08-01 09:31:58 | DEBUG | [DoubaoImageGenerator] 当前文本段落: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。
2025-08-01 09:32:02 | DEBUG | [DoubaoImageGenerator] 第53行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"status\\":1,\\"placeholder\\":{\\"width\\":1056,\\"height\\":1584}}}]}","id":"979aad1f-1b10-4ee3-a2bd-2b95d821bf2c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '26', 'event_type': 2001}
2025-08-01 09:32:02 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"status":1,"placeholder":{"width":1056,"height":1584}}}]}', 'id': '979aad1f-1b10-4ee3-a2bd-2b95d821bf2c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:32:02 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:32:02 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}]}
2025-08-01 09:32:02 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:32:02 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'status': 1, 'placeholder': {'width': 1056, 'height': 1584}}}
2025-08-01 09:32:02 | INFO | [DoubaoImageGenerator] 图片状态: 1
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 第55行事件数据: {'event_data': '{"message":{"content_type":2074,"content":"{\\"creations\\":[{\\"type\\":1,\\"image\\":{\\"key\\":\\"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c\\",\\"image_thumb\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371941\\\\u0026x-signature=UQoifmmUxl9o4G85TafUTyXyWQI%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"image_ori\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371941\\\\u0026x-signature=QsWsyquEgzrkJgoXwD7H%2FoERcT0%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_raw\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371941\\\\u0026x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536},\\"image_thumb_ori\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371941\\\\u0026x-signature=vzZ52hQstQ08amnp2WaTVO1F1ss%3D\\",\\"format\\":\\"jpeg\\",\\"width\\":386,\\"height\\":580},\\"description\\":\\"图片\\",\\"image_thumb_formats\\":{\\"avif\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371942\\\\u0026x-signature=ZklhyEcPwnX6bjgMo2NsnMvxDFY%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371942\\\\u0026x-signature=VeroRTldOdteKlGWmiXgWzk0Lzk%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"image_thumb_ori_formats\\":{\\"avif\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371942\\\\u0026x-signature=tXH6bFxxygnlAaAMyR0n3nCQR6I%3D\\",\\"format\\":\\"avif\\",\\"width\\":386,\\"height\\":580},\\"webp\\":{\\"url\\":\\"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371942\\\\u0026x-signature=Sxec3yE%2BkXlWFuDjFJBwJDtoOic%3D\\",\\"format\\":\\"webp\\",\\"width\\":386,\\"height\\":580}},\\"status\\":2,\\"gen_params\\":{\\"prompt\\":\\"人物姿势调整为侧身倚靠在自动扶梯扶手上，左手自然搭在扶手上方，右手提着包垂放在身侧，头部微微转向镜头方向\\",\\"neg_prompt\\":\\"画面模糊，低质量\\",\\"img_uri\\":\\"tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg\\"},\\"preview_img\\":{\\"url\\":\\"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\\\u0026rrcfp=5057214b\\\\u0026x-expires=2069371941\\\\u0026x-signature=8o%2F%2BH3PqaEWvX69Rr0lgSStadns%3D\\",\\"format\\":\\"png\\",\\"width\\":1024,\\"height\\":1536}}}]}","reset":true,"id":"979aad1f-1b10-4ee3-a2bd-2b95d821bf2c"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":4,"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781"}', 'event_id': '27', 'event_type': 2001}
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 2074, 'content': '{"creations":[{"type":1,"image":{"key":"tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c","image_thumb":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371941\\u0026x-signature=UQoifmmUxl9o4G85TafUTyXyWQI%3D","format":"jpeg","width":386,"height":580},"image_ori":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371941\\u0026x-signature=QsWsyquEgzrkJgoXwD7H%2FoERcT0%3D","format":"png","width":1024,"height":1536},"image_raw":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371941\\u0026x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D","format":"png","width":1024,"height":1536},"image_thumb_ori":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371941\\u0026x-signature=vzZ52hQstQ08amnp2WaTVO1F1ss%3D","format":"jpeg","width":386,"height":580},"description":"图片","image_thumb_formats":{"avif":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371942\\u0026x-signature=ZklhyEcPwnX6bjgMo2NsnMvxDFY%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371942\\u0026x-signature=VeroRTldOdteKlGWmiXgWzk0Lzk%3D","format":"webp","width":386,"height":580}},"image_thumb_ori_formats":{"avif":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371942\\u0026x-signature=tXH6bFxxygnlAaAMyR0n3nCQR6I%3D","format":"avif","width":386,"height":580},"webp":{"url":"https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371942\\u0026x-signature=Sxec3yE%2BkXlWFuDjFJBwJDtoOic%3D","format":"webp","width":386,"height":580}},"status":2,"gen_params":{"prompt":"人物姿势调整为侧身倚靠在自动扶梯扶手上，左手自然搭在扶手上方，右手提着包垂放在身侧，头部微微转向镜头方向","neg_prompt":"画面模糊，低质量","img_uri":"tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg"},"preview_img":{"url":"https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95\\u0026rrcfp=5057214b\\u0026x-expires=2069371941\\u0026x-signature=8o%2F%2BH3PqaEWvX69Rr0lgSStadns%3D","format":"png","width":1024,"height":1536}}}]}', 'reset': True, 'id': '979aad1f-1b10-4ee3-a2bd-2b95d821bf2c'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 4, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781'}
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 消息类型: 2074
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 图片内容: {'creations': [{'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c', 'image_thumb': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=UQoifmmUxl9o4G85TafUTyXyWQI%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=QsWsyquEgzrkJgoXwD7H%2FoERcT0%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vzZ52hQstQ08amnp2WaTVO1F1ss%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=ZklhyEcPwnX6bjgMo2NsnMvxDFY%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=VeroRTldOdteKlGWmiXgWzk0Lzk%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=tXH6bFxxygnlAaAMyR0n3nCQR6I%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=Sxec3yE%2BkXlWFuDjFJBwJDtoOic%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为侧身倚靠在自动扶梯扶手上，左手自然搭在扶手上方，右手提着包垂放在身侧，头部微微转向镜头方向', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg'}, 'preview_img': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=8o%2F%2BH3PqaEWvX69Rr0lgSStadns%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}]}
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 找到 1 个创作结果
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 创作结果 1: {'type': 1, 'image': {'key': 'tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c', 'image_thumb': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-watermark-v2.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=UQoifmmUxl9o4G85TafUTyXyWQI%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'image_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-download-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=QsWsyquEgzrkJgoXwD7H%2FoERcT0%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_raw': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D', 'format': 'png', 'width': 1024, 'height': 1536}, 'image_thumb_ori': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb.jpeg?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vzZ52hQstQ08amnp2WaTVO1F1ss%3D', 'format': 'jpeg', 'width': 386, 'height': 580}, 'description': '图片', 'image_thumb_formats': {'avif': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=ZklhyEcPwnX6bjgMo2NsnMvxDFY%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-wm-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=VeroRTldOdteKlGWmiXgWzk0Lzk%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'image_thumb_ori_formats': {'avif': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-avif.avif?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=tXH6bFxxygnlAaAMyR0n3nCQR6I%3D', 'format': 'avif', 'width': 386, 'height': 580}, 'webp': {'url': 'https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-thumb-webp.webp?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371942&x-signature=Sxec3yE%2BkXlWFuDjFJBwJDtoOic%3D', 'format': 'webp', 'width': 386, 'height': 580}}, 'status': 2, 'gen_params': {'prompt': '人物姿势调整为侧身倚靠在自动扶梯扶手上，左手自然搭在扶手上方，右手提着包垂放在身侧，头部微微转向镜头方向', 'neg_prompt': '画面模糊，低质量', 'img_uri': 'tos-cn-i-a9rns2rl98/fc1f55a993d4462f83a3051e0c53c385.jpg'}, 'preview_img': {'url': 'https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-web-preview-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=8o%2F%2BH3PqaEWvX69Rr0lgSStadns%3D', 'format': 'png', 'width': 1024, 'height': 1536}}}
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 图片状态: 2
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 图片生成成功，URL类型: image_raw, URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 创建配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。...', 图片=https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 第57行事件数据: {'event_data': '{"message":{"content_type":10000,"content":"{}","id":"e8226965-3395-4a9e-b458-bfb6af76457b"},"message_id":"14445726990936322","local_message_id":"d2f4f72f-3002-4b7a-8ea2-ef6b03e2965f","conversation_id":"*****************","local_conversation_id":"local_1754011911301049","section_id":"*****************","reply_id":"*****************","is_delta":true,"status":1,"is_finish":true,"message_action_bar":{},"input_content_type":2009,"message_index":2,"bot_id":"7338286299411103781","tts_content":"好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。"}', 'event_id': '28', 'event_type': 2001}
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 内部数据: {'message': {'content_type': 10000, 'content': '{}', 'id': 'e8226965-3395-4a9e-b458-bfb6af76457b'}, 'message_id': '14445726990936322', 'local_message_id': 'd2f4f72f-3002-4b7a-8ea2-ef6b03e2965f', 'conversation_id': '*****************', 'local_conversation_id': 'local_1754011911301049', 'section_id': '*****************', 'reply_id': '*****************', 'is_delta': True, 'status': 1, 'is_finish': True, 'message_action_bar': {}, 'input_content_type': 2009, 'message_index': 2, 'bot_id': '7338286299411103781', 'tts_content': '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。'}
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 消息类型: 10000
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] TTS文本响应: 好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。
2025-08-01 09:32:19 | DEBUG | [DoubaoImageGenerator] 第59行事件数据: {'event_data': '{}', 'event_id': '29', 'event_type': 2003}
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 处理剩余文本段落: '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。'
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 流式响应处理完成，共处理 60 行，耗时 24.8秒
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 总共收集到 2 个文本-图片配对
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。...', 图片=有
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 配对 2: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。...', 图片=无
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 图片处理流程完成，生成了 2 个文本-图片配对
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。...', 图片=有
2025-08-01 09:32:19 | INFO | [DoubaoImageGenerator] 配对 2: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。...', 图片=无
2025-08-01 09:32:19 | INFO | [DouBaoImageToImage] 豆包AI处理完成，耗时: 32.5秒
2025-08-01 09:32:19 | DEBUG | [DouBaoImageToImage] 标记临时文件清理: temp\doubao_image_to_image\quoted_image_1754011907.jpg
2025-08-01 09:32:19 | INFO | [DouBaoImageToImage] ✅ 豆包AI处理成功，生成了 2 个文本-图片配对
2025-08-01 09:32:19 | INFO | [DouBaoImageToImage] 完整文本响应: '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。'
2025-08-01 09:32:19 | INFO | [DouBaoImageToImage] ========== 豆包AI图生图处理完成 ==========
2025-08-01 09:32:19 | INFO | [DouBaoImageToImage] 豆包AI处理完成，生成了2个文本-图片配对
2025-08-01 09:32:19 | INFO | [DouBaoImageToImage] 处理配对 1: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。...', 图片=有
2025-08-01 09:32:19 | INFO | [DouBaoImageToImage] 发送文本 1: '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。'
2025-08-01 09:32:20 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。
2025-08-01 09:32:20 | INFO | [DouBaoImageToImage] 发送图片 1: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D
2025-08-01 09:32:20 | INFO | [DouBaoImageToImage] ========== 开始发送生成结果 ==========
2025-08-01 09:32:20 | INFO | [DouBaoImageToImage] 待发送图片数量: 1
2025-08-01 09:32:20 | INFO | [DouBaoImageToImage] 处理第 1/1 张图片...
2025-08-01 09:32:20 | DEBUG | [DouBaoImageToImage] 图片URL: https://p3-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/1385270d0fa341e784fda89bd1f71d9c~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069371941&x-signature=vlMIOkkdnULVi5JwVPPH4C677Jg%3D
2025-08-01 09:32:20 | DEBUG | [DouBaoImageToImage] 开始下载第 1 张图片...
2025-08-01 09:32:22 | INFO | [DouBaoImageToImage] 第 1 张图片下载完成，耗时: 2.4秒
2025-08-01 09:32:22 | INFO | [DouBaoImageToImage] 图片数据验证通过，大小: 1843.0KB
2025-08-01 09:32:22 | DEBUG | [DouBaoImageToImage] 开始发送第 1 张图片...
2025-08-01 09:32:29 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_to_image\quoted_image_1754011907.jpg
2025-08-01 09:32:31 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-08-01 09:32:31 | DEBUG | [DouBaoImageToImage] 发送结果: ('wxid_4usgcju5ey9q29_1754011943', 1754011957, 7960064155371986059)
2025-08-01 09:32:31 | INFO | [DouBaoImageToImage] ✅ 第 1/1 张图片发送成功，耗时: 8.9秒
2025-08-01 09:32:31 | DEBUG | [DouBaoImageToImage] 等待1.5秒后处理下一张图片...
2025-08-01 09:32:33 | INFO | [DouBaoImageToImage] ========== 图片发送完成 ==========
2025-08-01 09:32:33 | INFO | [DouBaoImageToImage] 发送结果: 成功 1/1 张
2025-08-01 09:32:33 | INFO | [DouBaoImageToImage] 处理配对 2: 文本='好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。...', 图片=无
2025-08-01 09:32:33 | INFO | [DouBaoImageToImage] 发送文本 2: '好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。'
2025-08-01 09:32:34 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:好的，我将为你上传的图片中的人物更换不同姿势，并将比例调整为2:3。
2025-08-01 09:32:34 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:32:34 | INFO |   - 消息内容: 豆包 换不同姿势
2025-08-01 09:32:34 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-01 09:32:34 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-01 09:32:34 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 换不同姿势</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>3039325562699437542</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;684415c9ca831ee2b5db0b9a8f9c34f7_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_9esKwq3j|v1_F1cA5tA5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="7177796a7167666b707a796d6e6a6f77" encryver="0" cdnthumbaeskey="7177796a7167666b707a796d6e6a6f77" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" cdnthumblength="3558" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" length="85864" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" hdlength="1487367" md5="f667f99f7e210556370f35f0560c2296"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754007194</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '3039325562699437542', 'NewMsgId': '3039325562699437542', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>684415c9ca831ee2b5db0b9a8f9c34f7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_9esKwq3j|v1_F1cA5tA5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754007194', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-01 09:32:34 | INFO |   - 引用消息ID: 
2025-08-01 09:32:34 | INFO |   - 引用消息类型: 
2025-08-01 09:32:34 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>豆包 换不同姿势</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>3039325562699437542</svrid>
			<fromusr>55878994168@chatroom</fromusr>
			<chatusr>wxid_4usgcju5ey9q29</chatusr>
			<displayname>瑶瑶</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;684415c9ca831ee2b5db0b9a8f9c34f7_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="85864" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;3&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_9esKwq3j|v1_F1cA5tA5&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="7177796a7167666b707a796d6e6a6f77" encryver="0" cdnthumbaeskey="7177796a7167666b707a796d6e6a6f77" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" cdnthumblength="3558" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" length="85864" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe020473d0533b0204688c069b042466643161626266312d383763382d346465312d393131632d3037643033613336373630640204052428010201000405004c537600cc39404d" hdlength="1487367" md5="f667f99f7e210556370f35f0560c2296"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1754007194</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 09:32:34 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-01 09:33:33 | DEBUG | 收到消息: {'MsgId': 1967318713, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="ac82bc0306ca9080bf464eedbab6861a" len = "42680" productid="" androidmd5="ac82bc0306ca9080bf464eedbab6861a" androidlen="42680" s60v3md5 = "ac82bc0306ca9080bf464eedbab6861a" s60v3len="42680" s60v5md5 = "ac82bc0306ca9080bf464eedbab6861a" s60v5len="42680" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=ac82bc0306ca9080bf464eedbab6861a&amp;filekey=30440201010430302e02016e0402534804206163383262633033303663613930383062663436346565646261623638363161020300a6b8040d00000004627466730000000132&amp;hy=SH&amp;storeid=2664d3a3f000d21f16024e6d50000006e01004fb1534811c3f031507c7512b&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=1e9c75c78798407a9c5246d14552110a&amp;filekey=30440201010430302e02016e0402534804203165396337356337383739383430376139633532343664313435353231313061020300a6c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2664d3a3f000dd0666024e6d50000006e02004fb2534811c3f031507c7513c&amp;ef=2&amp;bizid=1022" aeskey= "97660f2e636a41519f90f0cb46c4a683" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=8b65f548fa81884a148f11ef25fac211&amp;filekey=3043020101042f302d02016e040253480420386236356635343866613831383834613134386631316566323566616332313102024a60040d00000004627466730000000132&amp;hy=SH&amp;storeid=2664d3a3f000e4ea86024e6d50000006e03004fb3534811c3f031507c7514d&amp;ef=3&amp;bizid=1022" externmd5 = "cd2663b4ae5a7dc3d185e2fb3c5bbda4" width= "122" height= "105" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012018, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_bcOWugNl|v1_QkAIV3EB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 409411364288731883, 'MsgSeq': 871416328}
2025-08-01 09:33:33 | INFO | 收到表情消息: 消息ID:1967318713 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:ac82bc0306ca9080bf464eedbab6861a 大小:42680
2025-08-01 09:33:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 409411364288731883
2025-08-01 09:37:57 | DEBUG | 收到消息: {'MsgId': 863409751, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n[偷笑][偷笑][偷笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012282, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_y7/kC9ao|v1_9aMurXex</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4670459858081649873, 'MsgSeq': 871416329}
2025-08-01 09:37:57 | INFO | 收到表情消息: 消息ID:863409751 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:[偷笑][偷笑][偷笑]
2025-08-01 09:37:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4670459858081649873
2025-08-01 09:38:08 | DEBUG | 收到消息: {'MsgId': 411802349, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n@枂菟ིྀ\u2005宝子 今天打排位吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012292, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_5kipwrzramxr22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_LPIiyzRv|v1_YheJpQWs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1796164380423063674, 'MsgSeq': 871416330}
2025-08-01 09:38:08 | INFO | 收到文本消息: 消息ID:411802349 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:['wxid_5kipwrzramxr22'] 内容:@枂菟ིྀ 宝子 今天打排位吗
2025-08-01 09:38:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@枂菟ིྀ 宝子 今天打排位吗' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-01 09:38:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['@枂菟ིྀ\u2005宝子', '今天打排位吗']
2025-08-01 09:38:08 | DEBUG | 处理消息内容: '@枂菟ིྀ 宝子 今天打排位吗'
2025-08-01 09:38:08 | DEBUG | 消息内容 '@枂菟ིྀ 宝子 今天打排位吗' 不匹配任何命令，忽略
2025-08-01 09:38:11 | DEBUG | 收到消息: {'MsgId': 1110893327, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n[害羞][害羞]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012297, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_OtzUo1gW|v1_4AvjUOOQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5724198701130868638, 'MsgSeq': 871416331}
2025-08-01 09:38:11 | INFO | 收到表情消息: 消息ID:1110893327 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:[害羞][害羞]
2025-08-01 09:38:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5724198701130868638
2025-08-01 09:38:20 | DEBUG | 收到消息: {'MsgId': 583792814, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我都直接不按的</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2457086490123993747</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_yxdvl6zp4er522</chatusr>\n\t\t\t<displayname>胡好辣</displayname>\n\t\t\t<content>偶遇你欺负我</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777825750&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_RiHajKMY|v1_plFI0Hoc&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754011023</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012305, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>2f06e69645337f8dd7fe2fbe44864778_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_ERwC3+kC|v1_rlr+ZDUL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5256079109717806613, 'MsgSeq': 871416332}
2025-08-01 09:38:20 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-01 09:38:20 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:38:20 | INFO | 收到引用消息: 消息ID:583792814 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:我都直接不按的 引用类型:1
2025-08-01 09:38:20 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:38:20 | INFO | [DouBaoImageToImage] 消息内容: '我都直接不按的' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-01 09:38:20 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['我都直接不按的']
2025-08-01 09:38:20 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 09:38:20 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:38:20 | INFO |   - 消息内容: 我都直接不按的
2025-08-01 09:38:20 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-01 09:38:20 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-01 09:38:20 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '偶遇你欺负我', 'Msgid': '2457086490123993747', 'NewMsgId': '2457086490123993747', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '胡好辣', 'MsgSource': '<msgsource><sequence_id>777825750</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_RiHajKMY|v1_plFI0Hoc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754011023', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-01 09:38:20 | INFO |   - 引用消息ID: 
2025-08-01 09:38:20 | INFO |   - 引用消息类型: 
2025-08-01 09:38:20 | INFO |   - 引用消息内容: 偶遇你欺负我
2025-08-01 09:38:20 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-01 09:39:00 | DEBUG | 收到消息: {'MsgId': 1846140133, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n@Justོི\u2005晚点再看哈，还不知道有空没[憨笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012346, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_c3jkq1ylevnb12</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>8</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_RXlA5AID|v1_EqxV9QPR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6663040431105708081, 'MsgSeq': 871416333}
2025-08-01 09:39:00 | INFO | 收到文本消息: 消息ID:1846140133 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:['wxid_c3jkq1ylevnb12'] 内容:@Justོི 晚点再看哈，还不知道有空没[憨笑]
2025-08-01 09:39:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@Justོི 晚点再看哈，还不知道有空没[憨笑]' from wxid_5kipwrzramxr22 in 27852221909@chatroom
2025-08-01 09:39:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['@Justོི\u2005晚点再看哈，还不知道有空没[憨笑]']
2025-08-01 09:39:00 | DEBUG | 处理消息内容: '@Justོི 晚点再看哈，还不知道有空没[憨笑]'
2025-08-01 09:39:00 | DEBUG | 消息内容 '@Justོི 晚点再看哈，还不知道有空没[憨笑]' 不匹配任何命令，忽略
2025-08-01 09:39:06 | DEBUG | 收到消息: {'MsgId': 1947194613, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>好</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>6663040431105708081</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_5kipwrzramxr22</chatusr>\n\t\t\t<displayname>枂菟ིྀ</displayname>\n\t\t\t<content>@Justོི\u2005晚点再看哈，还不知道有空没[憨笑]</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777825823&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;wxid_c3jkq1ylevnb12&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;alnode&gt;\n\t\t&lt;inlenlist&gt;8&lt;/inlenlist&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_Qe/8IBWX|v1_nE6y3lgi&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754012346</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012352, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>95013729c4f9c7cafb0931d1cd1396ed_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_5lrU0ojF|v1_wouRU6RL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1714332492383554638, 'MsgSeq': 871416334}
2025-08-01 09:39:06 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-01 09:39:06 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 09:39:06 | INFO | 收到引用消息: 消息ID:1947194613 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:好 引用类型:1
2025-08-01 09:39:06 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-01 09:39:06 | INFO | [DouBaoImageToImage] 消息内容: '好' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-01 09:39:06 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['好']
2025-08-01 09:39:06 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-01 09:39:06 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 09:39:06 | INFO |   - 消息内容: 好
2025-08-01 09:39:06 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-01 09:39:06 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-01 09:39:06 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@Justོི\u2005晚点再看哈，还不知道有空没[憨笑]', 'Msgid': '6663040431105708081', 'NewMsgId': '6663040431105708081', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '枂菟ིྀ', 'MsgSource': '<msgsource><sequence_id>777825823</sequence_id>\n\t<atuserlist>wxid_c3jkq1ylevnb12</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>8</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Qe/8IBWX|v1_nE6y3lgi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754012346', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-01 09:39:06 | INFO |   - 引用消息ID: 
2025-08-01 09:39:06 | INFO |   - 引用消息类型: 
2025-08-01 09:39:06 | INFO |   - 引用消息内容: @Justོི 晚点再看哈，还不知道有空没[憨笑]
2025-08-01 09:39:06 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-01 09:39:43 | DEBUG | 收到消息: {'MsgId': 1419541434, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n[衰][衰]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012388, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_qCtsU2Go|v1_PLy9k9ZE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : [衰][衰]', 'NewMsgId': 2466586361082720114, 'MsgSeq': 871416335}
2025-08-01 09:39:43 | INFO | 收到表情消息: 消息ID:1419541434 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:[衰][衰]
2025-08-01 09:39:43 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2466586361082720114
2025-08-01 09:43:19 | DEBUG | 收到消息: {'MsgId': 488182318, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title />\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>8</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>2771423</totallen>\n\t\t\t<attachid>0:0:cf1ad3af1940ab2ee79c9c8605dd3f8b</attachid>\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5>cf1ad3af1940ab2ee79c9c8605dd3f8b</emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t\t<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba02042737949d0204688c1bbd042434323266643133372d643439322d346665352d613832372d6339653037386565646362300204052808030201000405004c543e007a819540</cdnthumburl>\n\t\t\t<cdnthumbaeskey>6573776d7a766f726363797463686777</cdnthumbaeskey>\n\t\t\t<cdnthumblength>50675</cdnthumblength>\n\t\t\t<cdnthumbwidth>300</cdnthumbwidth>\n\t\t\t<cdnthumbheight>533</cdnthumbheight>\n\t\t\t<cdnthumbmd5>ae58d9e46438ee6645e549f673222fff</cdnthumbmd5>\n\t\t\t<emojiinfo>CiBjZjFhZDNhZjE5NDBhYjJlZTc5YzljODYwNWRkM2Y4YhLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209Y2YxYWQzYWYxOTQwYWIyZWU3OWM5Yzg2MDVkZDNmOGImZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY2MzE2MTY0MzM2MTY2MzEzOTM0MzA2MTYyMzI2NTY1MzczOTYzMzk2MzM4MzYzMDM1NjQ2NDMzNjYzODYyMDIwMzJhNDlkZjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDM2NDY2M2QzNDQyY2IwMDAwMDA2ZTAxMDA0ZmIxNTM0ODAzZjM1MDMxNTA0ZjNiNGI4JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209M2M1MWY0OWMwNzUxZWRlZmU4ZTk2MmY5MGY4YTY3NGMmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjAzMzYzMzUzMTY2MzQzOTYzMzAzNzM1MzE2NTY0NjU2NjY1Mzg2NTM5MzYzMjY2MzkzMDY2Mzg2MTM2MzczNDYzMDIwMzJhNDllMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDYzZDIxM2QzNDQyY2IwMDAwMDA2ZTAyMDA0ZmIyNTM0ODAzZjM1MDMxNTA0ZjNiNGU1JmVmPTImYml6aWQ9MTAyMjIgNjcxNmRkMTZiNzQ1NGU0NjliOTlmOWE5MzNlNzE5MmM6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209Y2U1NjU0MGNlODE0YTU2NzAyNTI3Yjk4YTYxMTFkMTQmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY1MzUzNjM1MzQzMDYzNjUzODMxMzQ2MTM1MzYzNzMwMzIzNTMyMzc2MjM5Mzg2MTM2MzEzMTMxNjQzMTM0MDIwMzBjMWQ3MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDlkMjhiM2QzNDQyY2IwMDAwMDA2ZTAzMDA0ZmIzNTM0ODAzZjM1MDMxNTA0ZjNiNTMwJmVmPTMmYml6aWQ9MTAyMkogNTkzMjViYjVmODJiZjc3MGE0ZTQ3YjVkM2RiZDhlMTVaAGIAagCCAQA=</emojiinfo>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>1</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>300</thumbwidth>\n\t\t\t\t<thumbheight>533</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012605, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>7c19a69434b504671c863aa37830e71c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_AOlDWxIO|v1_0kmp+QoS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6604878297469652990, 'MsgSeq': 871416336}
2025-08-01 09:43:19 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-08-01 09:43:20 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<des />
		<username />
		<action>view</action>
		<type>8</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>2771423</totallen>
			<attachid>0:0:cf1ad3af1940ab2ee79c9c8605dd3f8b</attachid>
			<cdnattachurl />
			<emoticonmd5>cf1ad3af1940ab2ee79c9c8605dd3f8b</emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
			<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba02042737949d0204688c1bbd042434323266643133372d643439322d346665352d613832372d6339653037386565646362300204052808030201000405004c543e007a819540</cdnthumburl>
			<cdnthumbaeskey>6573776d7a766f726363797463686777</cdnthumbaeskey>
			<cdnthumblength>50675</cdnthumblength>
			<cdnthumbwidth>300</cdnthumbwidth>
			<cdnthumbheight>533</cdnthumbheight>
			<cdnthumbmd5>ae58d9e46438ee6645e549f673222fff</cdnthumbmd5>
			<emojiinfo>CiBjZjFhZDNhZjE5NDBhYjJlZTc5YzljODYwNWRkM2Y4YhLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209Y2YxYWQzYWYxOTQwYWIyZWU3OWM5Yzg2MDVkZDNmOGImZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY2MzE2MTY0MzM2MTY2MzEzOTM0MzA2MTYyMzI2NTY1MzczOTYzMzk2MzM4MzYzMDM1NjQ2NDMzNjYzODYyMDIwMzJhNDlkZjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDM2NDY2M2QzNDQyY2IwMDAwMDA2ZTAxMDA0ZmIxNTM0ODAzZjM1MDMxNTA0ZjNiNGI4JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209M2M1MWY0OWMwNzUxZWRlZmU4ZTk2MmY5MGY4YTY3NGMmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjAzMzYzMzUzMTY2MzQzOTYzMzAzNzM1MzE2NTY0NjU2NjY1Mzg2NTM5MzYzMjY2MzkzMDY2Mzg2MTM2MzczNDYzMDIwMzJhNDllMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDYzZDIxM2QzNDQyY2IwMDAwMDA2ZTAyMDA0ZmIyNTM0ODAzZjM1MDMxNTA0ZjNiNGU1JmVmPTImYml6aWQ9MTAyMjIgNjcxNmRkMTZiNzQ1NGU0NjliOTlmOWE5MzNlNzE5MmM6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209Y2U1NjU0MGNlODE0YTU2NzAyNTI3Yjk4YTYxMTFkMTQmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY1MzUzNjM1MzQzMDYzNjUzODMxMzQ2MTM1MzYzNzMwMzIzNTMyMzc2MjM5Mzg2MTM2MzEzMTMxNjQzMTM0MDIwMzBjMWQ3MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDlkMjhiM2QzNDQyY2IwMDAwMDA2ZTAzMDA0ZmIzNTM0ODAzZjM1MDMxNTA0ZjNiNTMwJmVmPTMmYml6aWQ9MTAyMkogNTkzMjViYjVmODJiZjc3MGE0ZTQ3YjVkM2RiZDhlMTVaAGIAagCCAQA=</emojiinfo>
		</appattach>
		<extinfo />
		<androidsource>1</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>300</thumbwidth>
				<thumbheight>533</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-01 09:43:20 | DEBUG | XML消息类型: 8
2025-08-01 09:43:20 | DEBUG | XML消息标题: None
2025-08-01 09:43:20 | DEBUG | XML消息描述: None
2025-08-01 09:43:20 | DEBUG | 附件信息 totallen: 2771423
2025-08-01 09:43:20 | DEBUG | 附件信息 attachid: 0:0:cf1ad3af1940ab2ee79c9c8605dd3f8b
2025-08-01 09:43:20 | DEBUG | 附件信息 emoticonmd5: cf1ad3af1940ab2ee79c9c8605dd3f8b
2025-08-01 09:43:20 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-01 09:43:20 | DEBUG | 附件信息 cdnthumburl: 3057020100044b304902010002041c3acaf602033d14ba02042737949d0204688c1bbd042434323266643133372d643439322d346665352d613832372d6339653037386565646362300204052808030201000405004c543e007a819540
2025-08-01 09:43:20 | DEBUG | 附件信息 cdnthumbaeskey: 6573776d7a766f726363797463686777
2025-08-01 09:43:20 | DEBUG | 附件信息 cdnthumblength: 50675
2025-08-01 09:43:20 | DEBUG | 附件信息 cdnthumbwidth: 300
2025-08-01 09:43:20 | DEBUG | 附件信息 cdnthumbheight: 533
2025-08-01 09:43:20 | DEBUG | 附件信息 cdnthumbmd5: ae58d9e46438ee6645e549f673222fff
2025-08-01 09:43:20 | DEBUG | 附件信息 emojiinfo: CiBjZjFhZDNhZjE5NDBhYjJlZTc5YzljODYwNWRkM2Y4YhLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209Y2YxYWQzYWYxOTQwYWIyZWU3OWM5Yzg2MDVkZDNmOGImZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY2MzE2MTY0MzM2MTY2MzEzOTM0MzA2MTYyMzI2NTY1MzczOTYzMzk2MzM4MzYzMDM1NjQ2NDMzNjYzODYyMDIwMzJhNDlkZjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDM2NDY2M2QzNDQyY2IwMDAwMDA2ZTAxMDA0ZmIxNTM0ODAzZjM1MDMxNTA0ZjNiNGI4JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209M2M1MWY0OWMwNzUxZWRlZmU4ZTk2MmY5MGY4YTY3NGMmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjAzMzYzMzUzMTY2MzQzOTYzMzAzNzM1MzE2NTY0NjU2NjY1Mzg2NTM5MzYzMjY2MzkzMDY2Mzg2MTM2MzczNDYzMDIwMzJhNDllMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDYzZDIxM2QzNDQyY2IwMDAwMDA2ZTAyMDA0ZmIyNTM0ODAzZjM1MDMxNTA0ZjNiNGU1JmVmPTImYml6aWQ9MTAyMjIgNjcxNmRkMTZiNzQ1NGU0NjliOTlmOWE5MzNlNzE5MmM6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209Y2U1NjU0MGNlODE0YTU2NzAyNTI3Yjk4YTYxMTFkMTQmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY1MzUzNjM1MzQzMDYzNjUzODMxMzQ2MTM1MzYzNzMwMzIzNTMyMzc2MjM5Mzg2MTM2MzEzMTMxNjQzMTM0MDIwMzBjMWQ3MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDlkMjhiM2QzNDQyY2IwMDAwMDA2ZTAzMDA0ZmIzNTM0ODAzZjM1MDMxNTA0ZjNiNTMwJmVmPTMmYml6aWQ9MTAyMkogNTkzMjViYjVmODJiZjc3MGE0ZTQ3YjVkM2RiZDhlMTVaAGIAagCCAQA=
2025-08-01 09:43:20 | INFO | 未知的XML消息类型: 8
2025-08-01 09:43:20 | INFO | 消息标题: None
2025-08-01 09:43:20 | INFO | 消息描述: None
2025-08-01 09:43:20 | INFO | 消息URL: N/A
2025-08-01 09:43:20 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<des />
		<username />
		<action>view</action>
		<type>8</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>2771423</totallen>
			<attachid>0:0:cf1ad3af1940ab2ee79c9c8605dd3f8b</attachid>
			<cdnattachurl />
			<emoticonmd5>cf1ad3af1940ab2ee79c9c8605dd3f8b</emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
			<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba02042737949d0204688c1bbd042434323266643133372d643439322d346665352d613832372d6339653037386565646362300204052808030201000405004c543e007a819540</cdnthumburl>
			<cdnthumbaeskey>6573776d7a766f726363797463686777</cdnthumbaeskey>
			<cdnthumblength>50675</cdnthumblength>
			<cdnthumbwidth>300</cdnthumbwidth>
			<cdnthumbheight>533</cdnthumbheight>
			<cdnthumbmd5>ae58d9e46438ee6645e549f673222fff</cdnthumbmd5>
			<emojiinfo>CiBjZjFhZDNhZjE5NDBhYjJlZTc5YzljODYwNWRkM2Y4YhLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209Y2YxYWQzYWYxOTQwYWIyZWU3OWM5Yzg2MDVkZDNmOGImZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY2MzE2MTY0MzM2MTY2MzEzOTM0MzA2MTYyMzI2NTY1MzczOTYzMzk2MzM4MzYzMDM1NjQ2NDMzNjYzODYyMDIwMzJhNDlkZjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDM2NDY2M2QzNDQyY2IwMDAwMDA2ZTAxMDA0ZmIxNTM0ODAzZjM1MDMxNTA0ZjNiNGI4JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209M2M1MWY0OWMwNzUxZWRlZmU4ZTk2MmY5MGY4YTY3NGMmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjAzMzYzMzUzMTY2MzQzOTYzMzAzNzM1MzE2NTY0NjU2NjY1Mzg2NTM5MzYzMjY2MzkzMDY2Mzg2MTM2MzczNDYzMDIwMzJhNDllMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDYzZDIxM2QzNDQyY2IwMDAwMDA2ZTAyMDA0ZmIyNTM0ODAzZjM1MDMxNTA0ZjNiNGU1JmVmPTImYml6aWQ9MTAyMjIgNjcxNmRkMTZiNzQ1NGU0NjliOTlmOWE5MzNlNzE5MmM6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209Y2U1NjU0MGNlODE0YTU2NzAyNTI3Yjk4YTYxMTFkMTQmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM0ODA0MjA2MzY1MzUzNjM1MzQzMDYzNjUzODMxMzQ2MTM1MzYzNzMwMzIzNTMyMzc2MjM5Mzg2MTM2MzEzMTMxNjQzMTM0MDIwMzBjMWQ3MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2NjczZjBlMjAwMDlkMjhiM2QzNDQyY2IwMDAwMDA2ZTAzMDA0ZmIzNTM0ODAzZjM1MDMxNTA0ZjNiNTMwJmVmPTMmYml6aWQ9MTAyMkogNTkzMjViYjVmODJiZjc3MGE0ZTQ3YjVkM2RiZDhlMTVaAGIAagCCAQA=</emojiinfo>
		</appattach>
		<extinfo />
		<androidsource>1</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>300</thumbwidth>
				<thumbheight>533</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-01 09:43:28 | DEBUG | 收到消息: {'MsgId': 1500711063, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title />\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>8</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>9270318</totallen>\n\t\t\t<attachid>0:0:fbc2d71de5efd7d50c3e87b483524cf4</attachid>\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5>fbc2d71de5efd7d50c3e87b483524cf4</emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t\t<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba0204a839949d0204688c1bc6042431316234623831332d386463662d346266632d396136362d3766336230323031366262650204052808030201000405004c4cd2007a819540</cdnthumburl>\n\t\t\t<cdnthumbaeskey>746a7972766267727877756370786e76</cdnthumbaeskey>\n\t\t\t<cdnthumblength>32043</cdnthumblength>\n\t\t\t<cdnthumbwidth>333</cdnthumbwidth>\n\t\t\t<cdnthumbheight>593</cdnthumbheight>\n\t\t\t<cdnthumbmd5>9e36e40edfcfd4067e974d8f289704aa</cdnthumbmd5>\n\t\t\t<emojiinfo>CiBmYmMyZDcxZGU1ZWZkN2Q1MGMzZTg3YjQ4MzUyNGNmNBKgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209ZmJjMmQ3MWRlNWVmZDdkNTBjM2U4N2I0ODM1MjRjZjQmZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGZiYzJkNzFkZTVlZmQ3ZDUwYzNlODdiNDgzNTI0Y2Y0MDIwNDAwOGQ3NDJlMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVjMDAwZDBiMzY5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMjFlZmIwMTE1NjliZjQ0ZTYmYml6aWQ9MTAyMyqgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjA4MjU4NWU5ZDQyMTUxNGNjMDUyMzUxMjJjMTg0MGImZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGIwODI1ODVlOWQ0MjE1MTRjYzA1MjM1MTIyYzE4NDBiMDIwNDAwOGQ3NDMwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVkMDAwYjY5ZGU5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMWE1ZjIwMTE1NjllZmJlZTgmYml6aWQ9MTAyMzIgOTQyNjZmOGVkYjkwODg2YzhhYTMzN2RlYjRiZTg4NDA6AEKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTBiYWYzYjY4NWQ0OGNmY2Q0YmFmNzNiN2NhNzkwY2ImZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMDUwYmFmM2I2ODVkNDhjZmNkNGJhZjczYjdjYTc5MGNiMDIwMzA5MjJlMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzI0NWFlYjAwMGQyMjU2NGU4NDg3NDQwMDAwMDEwNjAwMDA0ZjUwNTM1YTI0ZWZiMDExNTZhNzYxMjg5JmJpemlkPTEwMjNKIGVkNmMxNGRmOWViZGI4NTEzYjkwY2U4ZmNmNzhkNmZkggEA</emojiinfo>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>1</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>333</thumbwidth>\n\t\t\t\t<thumbheight>593</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012614, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>03d9127a3ff8d8d350668374964a9a09_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_7u711Sdl|v1_qBqbeger</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 7143950523709196729, 'MsgSeq': 871416337}
2025-08-01 09:43:28 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-08-01 09:43:28 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<des />
		<username />
		<action>view</action>
		<type>8</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>9270318</totallen>
			<attachid>0:0:fbc2d71de5efd7d50c3e87b483524cf4</attachid>
			<cdnattachurl />
			<emoticonmd5>fbc2d71de5efd7d50c3e87b483524cf4</emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
			<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba0204a839949d0204688c1bc6042431316234623831332d386463662d346266632d396136362d3766336230323031366262650204052808030201000405004c4cd2007a819540</cdnthumburl>
			<cdnthumbaeskey>746a7972766267727877756370786e76</cdnthumbaeskey>
			<cdnthumblength>32043</cdnthumblength>
			<cdnthumbwidth>333</cdnthumbwidth>
			<cdnthumbheight>593</cdnthumbheight>
			<cdnthumbmd5>9e36e40edfcfd4067e974d8f289704aa</cdnthumbmd5>
			<emojiinfo>CiBmYmMyZDcxZGU1ZWZkN2Q1MGMzZTg3YjQ4MzUyNGNmNBKgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209ZmJjMmQ3MWRlNWVmZDdkNTBjM2U4N2I0ODM1MjRjZjQmZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGZiYzJkNzFkZTVlZmQ3ZDUwYzNlODdiNDgzNTI0Y2Y0MDIwNDAwOGQ3NDJlMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVjMDAwZDBiMzY5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMjFlZmIwMTE1NjliZjQ0ZTYmYml6aWQ9MTAyMyqgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjA4MjU4NWU5ZDQyMTUxNGNjMDUyMzUxMjJjMTg0MGImZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGIwODI1ODVlOWQ0MjE1MTRjYzA1MjM1MTIyYzE4NDBiMDIwNDAwOGQ3NDMwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVkMDAwYjY5ZGU5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMWE1ZjIwMTE1NjllZmJlZTgmYml6aWQ9MTAyMzIgOTQyNjZmOGVkYjkwODg2YzhhYTMzN2RlYjRiZTg4NDA6AEKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTBiYWYzYjY4NWQ0OGNmY2Q0YmFmNzNiN2NhNzkwY2ImZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMDUwYmFmM2I2ODVkNDhjZmNkNGJhZjczYjdjYTc5MGNiMDIwMzA5MjJlMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzI0NWFlYjAwMGQyMjU2NGU4NDg3NDQwMDAwMDEwNjAwMDA0ZjUwNTM1YTI0ZWZiMDExNTZhNzYxMjg5JmJpemlkPTEwMjNKIGVkNmMxNGRmOWViZGI4NTEzYjkwY2U4ZmNmNzhkNmZkggEA</emojiinfo>
		</appattach>
		<extinfo />
		<androidsource>1</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>333</thumbwidth>
				<thumbheight>593</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-01 09:43:28 | DEBUG | XML消息类型: 8
2025-08-01 09:43:28 | DEBUG | XML消息标题: None
2025-08-01 09:43:28 | DEBUG | XML消息描述: None
2025-08-01 09:43:28 | DEBUG | 附件信息 totallen: 9270318
2025-08-01 09:43:28 | DEBUG | 附件信息 attachid: 0:0:fbc2d71de5efd7d50c3e87b483524cf4
2025-08-01 09:43:28 | DEBUG | 附件信息 emoticonmd5: fbc2d71de5efd7d50c3e87b483524cf4
2025-08-01 09:43:28 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-01 09:43:28 | DEBUG | 附件信息 cdnthumburl: 3057020100044b304902010002041c3acaf602033d14ba0204a839949d0204688c1bc6042431316234623831332d386463662d346266632d396136362d3766336230323031366262650204052808030201000405004c4cd2007a819540
2025-08-01 09:43:28 | DEBUG | 附件信息 cdnthumbaeskey: 746a7972766267727877756370786e76
2025-08-01 09:43:28 | DEBUG | 附件信息 cdnthumblength: 32043
2025-08-01 09:43:28 | DEBUG | 附件信息 cdnthumbwidth: 333
2025-08-01 09:43:28 | DEBUG | 附件信息 cdnthumbheight: 593
2025-08-01 09:43:28 | DEBUG | 附件信息 cdnthumbmd5: 9e36e40edfcfd4067e974d8f289704aa
2025-08-01 09:43:28 | DEBUG | 附件信息 emojiinfo: CiBmYmMyZDcxZGU1ZWZkN2Q1MGMzZTg3YjQ4MzUyNGNmNBKgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209ZmJjMmQ3MWRlNWVmZDdkNTBjM2U4N2I0ODM1MjRjZjQmZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGZiYzJkNzFkZTVlZmQ3ZDUwYzNlODdiNDgzNTI0Y2Y0MDIwNDAwOGQ3NDJlMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVjMDAwZDBiMzY5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMjFlZmIwMTE1NjliZjQ0ZTYmYml6aWQ9MTAyMyqgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjA4MjU4NWU5ZDQyMTUxNGNjMDUyMzUxMjJjMTg0MGImZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGIwODI1ODVlOWQ0MjE1MTRjYzA1MjM1MTIyYzE4NDBiMDIwNDAwOGQ3NDMwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVkMDAwYjY5ZGU5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMWE1ZjIwMTE1NjllZmJlZTgmYml6aWQ9MTAyMzIgOTQyNjZmOGVkYjkwODg2YzhhYTMzN2RlYjRiZTg4NDA6AEKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTBiYWYzYjY4NWQ0OGNmY2Q0YmFmNzNiN2NhNzkwY2ImZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMDUwYmFmM2I2ODVkNDhjZmNkNGJhZjczYjdjYTc5MGNiMDIwMzA5MjJlMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzI0NWFlYjAwMGQyMjU2NGU4NDg3NDQwMDAwMDEwNjAwMDA0ZjUwNTM1YTI0ZWZiMDExNTZhNzYxMjg5JmJpemlkPTEwMjNKIGVkNmMxNGRmOWViZGI4NTEzYjkwY2U4ZmNmNzhkNmZkggEA
2025-08-01 09:43:28 | INFO | 未知的XML消息类型: 8
2025-08-01 09:43:28 | INFO | 消息标题: None
2025-08-01 09:43:28 | INFO | 消息描述: None
2025-08-01 09:43:28 | INFO | 消息URL: N/A
2025-08-01 09:43:28 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<des />
		<username />
		<action>view</action>
		<type>8</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>9270318</totallen>
			<attachid>0:0:fbc2d71de5efd7d50c3e87b483524cf4</attachid>
			<cdnattachurl />
			<emoticonmd5>fbc2d71de5efd7d50c3e87b483524cf4</emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
			<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba0204a839949d0204688c1bc6042431316234623831332d386463662d346266632d396136362d3766336230323031366262650204052808030201000405004c4cd2007a819540</cdnthumburl>
			<cdnthumbaeskey>746a7972766267727877756370786e76</cdnthumbaeskey>
			<cdnthumblength>32043</cdnthumblength>
			<cdnthumbwidth>333</cdnthumbwidth>
			<cdnthumbheight>593</cdnthumbheight>
			<cdnthumbmd5>9e36e40edfcfd4067e974d8f289704aa</cdnthumbmd5>
			<emojiinfo>CiBmYmMyZDcxZGU1ZWZkN2Q1MGMzZTg3YjQ4MzUyNGNmNBKgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209ZmJjMmQ3MWRlNWVmZDdkNTBjM2U4N2I0ODM1MjRjZjQmZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGZiYzJkNzFkZTVlZmQ3ZDUwYzNlODdiNDgzNTI0Y2Y0MDIwNDAwOGQ3NDJlMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVjMDAwZDBiMzY5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMjFlZmIwMTE1NjliZjQ0ZTYmYml6aWQ9MTAyMyqgAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjA4MjU4NWU5ZDQyMTUxNGNjMDUyMzUxMjJjMTg0MGImZmlsZWtleT0zMDM2MDIwMTAxMDQyMjMwMjAwMjAyMDEwNjA0MDI1MzVhMDQxMGIwODI1ODVlOWQ0MjE1MTRjYzA1MjM1MTIyYzE4NDBiMDIwNDAwOGQ3NDMwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3MjQyYTVkMDAwYjY5ZGU5NDc2YmJkYjAwMDAwMTA2MDAwMDRmNTA1MzVhMWE1ZjIwMTE1NjllZmJlZTgmYml6aWQ9MTAyMzIgOTQyNjZmOGVkYjkwODg2YzhhYTMzN2RlYjRiZTg4NDA6AEKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTBiYWYzYjY4NWQ0OGNmY2Q0YmFmNzNiN2NhNzkwY2ImZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMDUwYmFmM2I2ODVkNDhjZmNkNGJhZjczYjdjYTc5MGNiMDIwMzA5MjJlMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzI0NWFlYjAwMGQyMjU2NGU4NDg3NDQwMDAwMDEwNjAwMDA0ZjUwNTM1YTI0ZWZiMDExNTZhNzYxMjg5JmJpemlkPTEwMjNKIGVkNmMxNGRmOWViZGI4NTEzYjkwY2U4ZmNmNzhkNmZkggEA</emojiinfo>
		</appattach>
		<extinfo />
		<androidsource>1</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>333</thumbwidth>
				<thumbheight>593</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-01 09:43:41 | DEBUG | 收到消息: {'MsgId': 100682804, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title />\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>8</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>2770203</totallen>\n\t\t\t<attachid>0:0:b2f8af53daa4824e65df92c1a69098e6</attachid>\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5>b2f8af53daa4824e65df92c1a69098e6</emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t\t<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba0204d506ff9d0204688c1bd2042439386534363764622d396137342d343462392d613039322d3236393336356563343037660204052808030201000405004c5056007a819540</cdnthumburl>\n\t\t\t<cdnthumbaeskey>627a73676763737a6f7078627a6e7969</cdnthumbaeskey>\n\t\t\t<cdnthumblength>55971</cdnthumblength>\n\t\t\t<cdnthumbwidth>377</cdnthumbwidth>\n\t\t\t<cdnthumbheight>671</cdnthumbheight>\n\t\t\t<cdnthumbmd5>2e3af5165d4c6378623c13aa522c311b</cdnthumbmd5>\n\t\t\t<emojiinfo>CiBiMmY4YWY1M2RhYTQ4MjRlNjVkZjkyYzFhNjkwOThlNhKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjJmOGFmNTNkYWE0ODI0ZTY1ZGY5MmMxYTY5MDk4ZTYmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMGIyZjhhZjUzZGFhNDgyNGU2NWRmOTJjMWE2OTA5OGU2MDIwMzJhNDUxYjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzRmZDZlMzAwMDQxMmQ2YjU5MTYxZjEwMDAwMDEwNjAwMDA0ZjUwNTM1YTA4ZmYxMDExNTY4OTgyYThiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTAwODJkZGUyZmJkYmQ2MDlhZmI5MWJhODk2M2Q4Y2MwJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM1YTA0MTAwMDgyZGRlMmZiZGJkNjA5YWZiOTFiYTg5NjNkOGNjMDAyMDMyYTQ1MjAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U1omc3RvcmVpZD0yNjc0ZmQ2ZTMwMDBhZGJmOWI1OTE2MWYxMDAwMDAxMDYwMDAwNGY1MDUzNWEwYzQ2NmJjMWU3MjY1MjYxMyZiaXppZD0xMDIzMiA1NzMwMzc3MDE4ZTUyYWMzNmUzZGNkZjI0OTU0NjVlOToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT05Nzc2ZmVlMTA4YWVkNWE5ODRmOThlOTI4ZDlhZGIxOSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNWEwNDEwOTc3NmZlZTEwOGFlZDVhOTg0Zjk4ZTkyOGQ5YWRiMTkwMjAzMDdlMzUwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3NGZkNmU1MDAwZjM4YThiNTkxNjFmMTAwMDAwMTA2MDAwMDRmNTA1MzVhMDhmZjEwMTE1Njg5ODJjZWEmYml6aWQ9MTAyM0ogMjkzNjgxMTBiYzIyZjEyNmM2NzY3N2MzZDljM2JmMjGCAQA=</emojiinfo>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>1</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>377</thumbwidth>\n\t\t\t\t<thumbheight>671</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754012626, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9613c7634c0f0a24c7094953a0203430_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_J0qX00pi|v1_uYyBvrnF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 656179398968061220, 'MsgSeq': 871416338}
2025-08-01 09:43:41 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-08-01 09:43:41 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<des />
		<username />
		<action>view</action>
		<type>8</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>2770203</totallen>
			<attachid>0:0:b2f8af53daa4824e65df92c1a69098e6</attachid>
			<cdnattachurl />
			<emoticonmd5>b2f8af53daa4824e65df92c1a69098e6</emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
			<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba0204d506ff9d0204688c1bd2042439386534363764622d396137342d343462392d613039322d3236393336356563343037660204052808030201000405004c5056007a819540</cdnthumburl>
			<cdnthumbaeskey>627a73676763737a6f7078627a6e7969</cdnthumbaeskey>
			<cdnthumblength>55971</cdnthumblength>
			<cdnthumbwidth>377</cdnthumbwidth>
			<cdnthumbheight>671</cdnthumbheight>
			<cdnthumbmd5>2e3af5165d4c6378623c13aa522c311b</cdnthumbmd5>
			<emojiinfo>CiBiMmY4YWY1M2RhYTQ4MjRlNjVkZjkyYzFhNjkwOThlNhKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjJmOGFmNTNkYWE0ODI0ZTY1ZGY5MmMxYTY5MDk4ZTYmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMGIyZjhhZjUzZGFhNDgyNGU2NWRmOTJjMWE2OTA5OGU2MDIwMzJhNDUxYjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzRmZDZlMzAwMDQxMmQ2YjU5MTYxZjEwMDAwMDEwNjAwMDA0ZjUwNTM1YTA4ZmYxMDExNTY4OTgyYThiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTAwODJkZGUyZmJkYmQ2MDlhZmI5MWJhODk2M2Q4Y2MwJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM1YTA0MTAwMDgyZGRlMmZiZGJkNjA5YWZiOTFiYTg5NjNkOGNjMDAyMDMyYTQ1MjAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U1omc3RvcmVpZD0yNjc0ZmQ2ZTMwMDBhZGJmOWI1OTE2MWYxMDAwMDAxMDYwMDAwNGY1MDUzNWEwYzQ2NmJjMWU3MjY1MjYxMyZiaXppZD0xMDIzMiA1NzMwMzc3MDE4ZTUyYWMzNmUzZGNkZjI0OTU0NjVlOToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT05Nzc2ZmVlMTA4YWVkNWE5ODRmOThlOTI4ZDlhZGIxOSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNWEwNDEwOTc3NmZlZTEwOGFlZDVhOTg0Zjk4ZTkyOGQ5YWRiMTkwMjAzMDdlMzUwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3NGZkNmU1MDAwZjM4YThiNTkxNjFmMTAwMDAwMTA2MDAwMDRmNTA1MzVhMDhmZjEwMTE1Njg5ODJjZWEmYml6aWQ9MTAyM0ogMjkzNjgxMTBiYzIyZjEyNmM2NzY3N2MzZDljM2JmMjGCAQA=</emojiinfo>
		</appattach>
		<extinfo />
		<androidsource>1</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>377</thumbwidth>
				<thumbheight>671</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-01 09:43:41 | DEBUG | XML消息类型: 8
2025-08-01 09:43:41 | DEBUG | XML消息标题: None
2025-08-01 09:43:41 | DEBUG | XML消息描述: None
2025-08-01 09:43:41 | DEBUG | 附件信息 totallen: 2770203
2025-08-01 09:43:41 | DEBUG | 附件信息 attachid: 0:0:b2f8af53daa4824e65df92c1a69098e6
2025-08-01 09:43:41 | DEBUG | 附件信息 emoticonmd5: b2f8af53daa4824e65df92c1a69098e6
2025-08-01 09:43:41 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-01 09:43:41 | DEBUG | 附件信息 cdnthumburl: 3057020100044b304902010002041c3acaf602033d14ba0204d506ff9d0204688c1bd2042439386534363764622d396137342d343462392d613039322d3236393336356563343037660204052808030201000405004c5056007a819540
2025-08-01 09:43:41 | DEBUG | 附件信息 cdnthumbaeskey: 627a73676763737a6f7078627a6e7969
2025-08-01 09:43:41 | DEBUG | 附件信息 cdnthumblength: 55971
2025-08-01 09:43:41 | DEBUG | 附件信息 cdnthumbwidth: 377
2025-08-01 09:43:41 | DEBUG | 附件信息 cdnthumbheight: 671
2025-08-01 09:43:41 | DEBUG | 附件信息 cdnthumbmd5: 2e3af5165d4c6378623c13aa522c311b
2025-08-01 09:43:41 | DEBUG | 附件信息 emojiinfo: CiBiMmY4YWY1M2RhYTQ4MjRlNjVkZjkyYzFhNjkwOThlNhKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjJmOGFmNTNkYWE0ODI0ZTY1ZGY5MmMxYTY5MDk4ZTYmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMGIyZjhhZjUzZGFhNDgyNGU2NWRmOTJjMWE2OTA5OGU2MDIwMzJhNDUxYjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzRmZDZlMzAwMDQxMmQ2YjU5MTYxZjEwMDAwMDEwNjAwMDA0ZjUwNTM1YTA4ZmYxMDExNTY4OTgyYThiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTAwODJkZGUyZmJkYmQ2MDlhZmI5MWJhODk2M2Q4Y2MwJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM1YTA0MTAwMDgyZGRlMmZiZGJkNjA5YWZiOTFiYTg5NjNkOGNjMDAyMDMyYTQ1MjAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U1omc3RvcmVpZD0yNjc0ZmQ2ZTMwMDBhZGJmOWI1OTE2MWYxMDAwMDAxMDYwMDAwNGY1MDUzNWEwYzQ2NmJjMWU3MjY1MjYxMyZiaXppZD0xMDIzMiA1NzMwMzc3MDE4ZTUyYWMzNmUzZGNkZjI0OTU0NjVlOToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT05Nzc2ZmVlMTA4YWVkNWE5ODRmOThlOTI4ZDlhZGIxOSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNWEwNDEwOTc3NmZlZTEwOGFlZDVhOTg0Zjk4ZTkyOGQ5YWRiMTkwMjAzMDdlMzUwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3NGZkNmU1MDAwZjM4YThiNTkxNjFmMTAwMDAwMTA2MDAwMDRmNTA1MzVhMDhmZjEwMTE1Njg5ODJjZWEmYml6aWQ9MTAyM0ogMjkzNjgxMTBiYzIyZjEyNmM2NzY3N2MzZDljM2JmMjGCAQA=
2025-08-01 09:43:41 | INFO | 未知的XML消息类型: 8
2025-08-01 09:43:41 | INFO | 消息标题: None
2025-08-01 09:43:41 | INFO | 消息描述: None
2025-08-01 09:43:41 | INFO | 消息URL: N/A
2025-08-01 09:43:41 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<des />
		<username />
		<action>view</action>
		<type>8</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>2770203</totallen>
			<attachid>0:0:b2f8af53daa4824e65df92c1a69098e6</attachid>
			<cdnattachurl />
			<emoticonmd5>b2f8af53daa4824e65df92c1a69098e6</emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
			<cdnthumburl>3057020100044b304902010002041c3acaf602033d14ba0204d506ff9d0204688c1bd2042439386534363764622d396137342d343462392d613039322d3236393336356563343037660204052808030201000405004c5056007a819540</cdnthumburl>
			<cdnthumbaeskey>627a73676763737a6f7078627a6e7969</cdnthumbaeskey>
			<cdnthumblength>55971</cdnthumblength>
			<cdnthumbwidth>377</cdnthumbwidth>
			<cdnthumbheight>671</cdnthumbheight>
			<cdnthumbmd5>2e3af5165d4c6378623c13aa522c311b</cdnthumbmd5>
			<emojiinfo>CiBiMmY4YWY1M2RhYTQ4MjRlNjVkZjkyYzFhNjkwOThlNhKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209YjJmOGFmNTNkYWE0ODI0ZTY1ZGY5MmMxYTY5MDk4ZTYmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzVhMDQxMGIyZjhhZjUzZGFhNDgyNGU2NWRmOTJjMWE2OTA5OGU2MDIwMzJhNDUxYjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NzRmZDZlMzAwMDQxMmQ2YjU5MTYxZjEwMDAwMDEwNjAwMDA0ZjUwNTM1YTA4ZmYxMDExNTY4OTgyYThiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTAwODJkZGUyZmJkYmQ2MDlhZmI5MWJhODk2M2Q4Y2MwJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM1YTA0MTAwMDgyZGRlMmZiZGJkNjA5YWZiOTFiYTg5NjNkOGNjMDAyMDMyYTQ1MjAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U1omc3RvcmVpZD0yNjc0ZmQ2ZTMwMDBhZGJmOWI1OTE2MWYxMDAwMDAxMDYwMDAwNGY1MDUzNWEwYzQ2NmJjMWU3MjY1MjYxMyZiaXppZD0xMDIzMiA1NzMwMzc3MDE4ZTUyYWMzNmUzZGNkZjI0OTU0NjVlOToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT05Nzc2ZmVlMTA4YWVkNWE5ODRmOThlOTI4ZDlhZGIxOSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNWEwNDEwOTc3NmZlZTEwOGFlZDVhOTg0Zjk4ZTkyOGQ5YWRiMTkwMjAzMDdlMzUwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNaJnN0b3JlaWQ9MjY3NGZkNmU1MDAwZjM4YThiNTkxNjFmMTAwMDAwMTA2MDAwMDRmNTA1MzVhMDhmZjEwMTE1Njg5ODJjZWEmYml6aWQ9MTAyM0ogMjkzNjgxMTBiYzIyZjEyNmM2NzY3N2MzZDljM2JmMjGCAQA=</emojiinfo>
		</appattach>
		<extinfo />
		<androidsource>1</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>377</thumbwidth>
				<thumbheight>671</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

