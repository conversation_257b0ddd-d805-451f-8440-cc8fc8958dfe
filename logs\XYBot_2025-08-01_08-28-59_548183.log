2025-08-01 08:29:00 | SUCCESS | 读取主设置成功
2025-08-01 08:29:00 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 08:29:00 | INFO | 2025/08/01 08:29:00 GetRedisAddr: 127.0.0.1:6379
2025-08-01 08:29:00 | INFO | 2025/08/01 08:29:00 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 08:29:00 | INFO | 2025/08/01 08:29:00 Server start at :9000
2025-08-01 08:29:01 | SUCCESS | WechatAPI服务已启动
2025-08-01 08:29:01 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 08:29:01 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 08:29:01 | SUCCESS | 登录成功
2025-08-01 08:29:01 | SUCCESS | 已开启自动心跳
2025-08-01 08:29:01 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 08:29:01 | SUCCESS | 数据库初始化成功
2025-08-01 08:29:01 | SUCCESS | 定时任务已启动
2025-08-01 08:29:01 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 08:29:02 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 08:29:02 | INFO | 播客API初始化成功
2025-08-01 08:29:02 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 08:29:02 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 08:29:02 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 08:29:02 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 08:29:02 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 08:29:02 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 08:29:02 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 08:29:02 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 08:29:02 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 08:29:03 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 08:29:03 | ERROR | 加载 DoubaoDrawing 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 94, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{dirname}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\DoubaoDrawing\__init__.py", line 1, in <module>
    from .main import DoubaoDrawing
  File "C:\XYBotV2\plugins\DoubaoDrawing\main.py", line 200
    import os, json, tomllib, re, time, random, uuid, httpx, asyncio, aiofiles
IndentationError: expected an indented block after 'try' statement on line 199

2025-08-01 08:29:03 | SUCCESS | 已加载插件: False
2025-08-01 08:29:03 | INFO | 处理堆积消息中
2025-08-01 08:29:03 | SUCCESS | 处理堆积消息完毕
2025-08-01 08:29:03 | SUCCESS | 开始处理消息
2025-08-01 08:29:42 | DEBUG | 收到消息: {'MsgId': 818153508, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n你不是驾照早都有了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008187, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_za6jyY0h|v1_ZxMhnNMF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 你不是驾照早都有了', 'NewMsgId': 7865398367910587107, 'MsgSeq': 871416115}
2025-08-01 08:29:42 | INFO | 收到文本消息: 消息ID:818153508 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:你不是驾照早都有了
2025-08-01 08:29:45 | DEBUG | 收到消息: {'MsgId': 1355092048, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008191, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_vsRsM/lt|v1_kH9RdL0c</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : [抠鼻]', 'NewMsgId': 3400752330898741887, 'MsgSeq': 871416116}
2025-08-01 08:29:45 | INFO | 收到表情消息: 消息ID:1355092048 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:[抠鼻]
