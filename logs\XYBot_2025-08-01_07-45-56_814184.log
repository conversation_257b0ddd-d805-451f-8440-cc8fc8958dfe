2025-08-01 07:45:58 | SUCCESS | 读取主设置成功
2025-08-01 07:45:58 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 07:45:58 | INFO | 2025/08/01 07:45:58 GetRedisAddr: 127.0.0.1:6379
2025-08-01 07:45:58 | INFO | 2025/08/01 07:45:58 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 07:45:58 | INFO | 2025/08/01 07:45:58 Server start at :9000
2025-08-01 07:45:58 | SUCCESS | WechatAPI服务已启动
2025-08-01 07:45:59 | SUCCESS | 获取到登录uuid: QbkUULF9nBlKg9eUnGTU
2025-08-01 07:45:59 | INFO | 等待登录中，过期倒计时：239
2025-08-01 07:46:06 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 07:46:06 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 07:46:06 | SUCCESS | 登录成功
2025-08-01 07:46:06 | SUCCESS | 已开启自动心跳
2025-08-01 07:46:06 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 07:46:06 | SUCCESS | 数据库初始化成功
2025-08-01 07:46:06 | SUCCESS | 定时任务已启动
2025-08-01 07:46:06 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 07:46:06 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 07:46:07 | INFO | 播客API初始化成功
2025-08-01 07:46:07 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 07:46:07 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 07:46:07 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 07:46:07 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 07:46:07 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 07:46:07 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 07:46:07 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 07:46:07 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 07:46:07 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 07:46:08 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 07:46:08 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 07:46:08 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 07:46:08 | DEBUG |   - 启用状态: True
2025-08-01 07:46:08 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 07:46:08 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 07:46:08 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 07:46:08 | DEBUG |   - Cookies配置: 已配置
2025-08-01 07:46:08 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 07:46:08 | DEBUG |   - 自然化响应: True
2025-08-01 07:46:08 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 07:46:08 | ERROR | 加载插件时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 51, in load_plugin
    plugin = plugin_class()
             ^^^^^^^^^^^^^^
  File "C:\XYBotV2\plugins\MiniProgramTester\main.py", line 24, in __init__
    self._init_natural_responses()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MiniProgramTester' object has no attribute '_init_natural_responses'

2025-08-01 07:46:08 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-01 07:46:08 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 07:46:08 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 07:46:08 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 07:46:08 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 07:46:08 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 07:46:08 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 07:46:08 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 07:46:08 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 07:46:08 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 07:46:08 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 07:46:08 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 07:46:08 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 07:46:08 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 07:46:08 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 07:46:09 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 07:46:09 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 07:46:09 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 07:46:10 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 07:46:10 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 07:46:10 | INFO | [yuanbao] 插件初始化完成
2025-08-01 07:46:10 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 07:46:10 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 07:46:10 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 07:46:10 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 07:46:10 | INFO | 处理堆积消息中
2025-08-01 07:46:13 | DEBUG | 接受到 22 条消息
2025-08-01 07:46:15 | DEBUG | 接受到 1 条消息
2025-08-01 07:46:16 | SUCCESS | 处理堆积消息完毕
2025-08-01 07:46:16 | SUCCESS | 开始处理消息
2025-08-01 07:46:19 | DEBUG | 收到消息: {'MsgId': 784681069, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '\n\t\t\t\t<sysmsg type="ClientCheckGetExtInfo">\n\t\t\t\t\t<ClientCheckGetExtInfo>\n\t\t\t\t\t\t<ReportContext>539033600</ReportContext>\n\t\t\t\t\t\t<Basic>0</Basic>\n                        <Cellular>1</Cellular>\n\t\t\t\t\t</ClientCheckGetExtInfo>\n\t\t\t\t</sysmsg>\n\t\t\t'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005584, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7095035852012009233, 'MsgSeq': 871416051}
2025-08-01 07:46:19 | DEBUG | 系统消息类型: ClientCheckGetExtInfo
2025-08-01 07:46:24 | DEBUG | 收到消息: {'MsgId': 194049194, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="e9274f992a3708c1ee6751bd1f54cb8d" len="341750" productid="" androidmd5="e9274f992a3708c1ee6751bd1f54cb8d" androidlen="341750" s60v3md5="e9274f992a3708c1ee6751bd1f54cb8d" s60v3len="341750" s60v5md5="e9274f992a3708c1ee6751bd1f54cb8d" s60v5len="341750" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=e9274f992a3708c1ee6751bd1f54cb8d&amp;filekey=30440201010430302e02016e040253480420653932373466393932613337303863316565363735316264316635346362386402030536f6040d00000004627466730000000132&amp;hy=SH&amp;storeid=268490e700007ab7540015af20000006e01004fb153480a33203156935a24c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=919b8492244ac249fd9779579a9ab681&amp;filekey=30440201010430302e02016e04025348042039313962383439323234346163323439666439373739353739613961623638310203053700040d00000004627466730000000132&amp;hy=SH&amp;storeid=268490e700008d35040015af20000006e02004fb253480a33203156935a257&amp;ef=2&amp;bizid=1022" aeskey="86a38143347b4e8bb2d2fa540cf3d2a3" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=03577113a548d58b306938b5629402c1&amp;filekey=30440201010430302e02016e040253480420303335373731313361353438643538623330363933386235363239343032633102030093d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268490e70000a922740015af20000006e03004fb353480a33203156935a279&amp;ef=3&amp;bizid=1022" externmd5="736709373b268fbbf96b39726d17c0fe" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005589, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_FQ/rBTzs|v1_bmrgtDTO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 7722718510881639363, 'MsgSeq': 871416052}
2025-08-01 07:46:24 | INFO | 收到表情消息: 消息ID:194049194 来自:48097389945@chatroom 发送人:xiaomaochong MD5:e9274f992a3708c1ee6751bd1f54cb8d 大小:341750
2025-08-01 07:46:25 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7722718510881639363
2025-08-01 07:49:07 | DEBUG | 收到消息: {'MsgId': 1943861800, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_0o755tqnn7p22:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005752, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Tl2Tjy9e|v1_0PCNxm3T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 419604913461712387, 'MsgSeq': 871416053}
2025-08-01 07:49:07 | INFO | 收到文本消息: 消息ID:1943861800 来自:27852221909@chatroom 发送人:wxid_0o755tqnn7p22 @:[] 内容:签到
2025-08-01 07:49:07 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 07:49:07 | DEBUG | 处理消息内容: '签到'
2025-08-01 07:49:07 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-08-01 07:49:07 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-01 07:49:07 | INFO | 数据库: 用户wxid_0o755tqnn7p22登录时间设置为2025-08-01 00:00:00+08:00
2025-08-01 07:49:07 | INFO | 数据库: 用户wxid_0o755tqnn7p22连续签到天数设置为1
2025-08-01 07:49:07 | INFO | 数据库: 用户wxid_0o755tqnn7p22积分增加16
2025-08-01 07:49:09 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['wxid_0o755tqnn7p22'] 内容:@十一🕶 
-----XYBot-----
签到成功！你领到了 16 个积分！✅
你是今天第 1 个签到的！🎉
你断开了 1 天的连续签到！[心碎]
2025-08-01 07:49:20 | DEBUG | 收到消息: {'MsgId': 66427674, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n兄弟们 周五了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005765, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_NQ6mLRbw|v1_6y4lHafY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 兄弟们 周五了', 'NewMsgId': 403906159717760817, 'MsgSeq': 871416058}
2025-08-01 07:49:20 | INFO | 收到文本消息: 消息ID:66427674 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:兄弟们 周五了
2025-08-01 07:49:20 | DEBUG | 处理消息内容: '兄弟们 周五了'
2025-08-01 07:49:20 | DEBUG | 消息内容 '兄弟们 周五了' 不匹配任何命令，忽略
2025-08-01 07:49:27 | DEBUG | 收到消息: {'MsgId': 1623368133, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_0o755tqnn7p22:\n打卡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005772, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_VuZsU7M1|v1_jhRYoVAn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6854043306918913092, 'MsgSeq': 871416059}
2025-08-01 07:49:27 | INFO | 收到文本消息: 消息ID:1623368133 来自:27852221909@chatroom 发送人:wxid_0o755tqnn7p22 @:[] 内容:打卡
2025-08-01 07:49:27 | DEBUG | 处理消息内容: '打卡'
2025-08-01 07:49:27 | DEBUG | 消息内容 '打卡' 不匹配任何命令，忽略
2025-08-01 07:49:39 | DEBUG | 收到消息: {'MsgId': 1031814803, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_0o755tqnn7p22:\n[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005784, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_lP7hZiPr|v1_Kk56dlm3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6868261722346981275, 'MsgSeq': 871416060}
2025-08-01 07:49:39 | INFO | 收到表情消息: 消息ID:1031814803 来自:27852221909@chatroom 发送人:wxid_0o755tqnn7p22 @:[] 内容:[抠鼻]
2025-08-01 07:49:39 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6868261722346981275
2025-08-01 07:49:57 | DEBUG | 收到消息: {'MsgId': 1194531895, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n@【微信红包】收到一个微信红包\u2005赚了多少[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005802, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[zll953369865]]></atuserlist>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>16</inlenlist>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_zdFo0Qk+|v1_/w0DUStD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : @【微信红包】收到一个微信红包\u2005赚了多少[抠鼻]', 'NewMsgId': 790757609173415434, 'MsgSeq': 871416061}
2025-08-01 07:49:57 | INFO | 收到文本消息: 消息ID:1194531895 来自:48097389945@chatroom 发送人:xiaomaochong @:['zll953369865'] 内容:@【微信红包】收到一个微信红包 赚了多少[抠鼻]
2025-08-01 07:49:58 | DEBUG | 处理消息内容: '@【微信红包】收到一个微信红包 赚了多少[抠鼻]'
2025-08-01 07:49:58 | DEBUG | 消息内容 '@【微信红包】收到一个微信红包 赚了多少[抠鼻]' 不匹配任何命令，忽略
2025-08-01 07:50:08 | DEBUG | 收到消息: {'MsgId': 1484659106, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n赚了一头牛'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005813, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_RyB89xvU|v1_eJ7oN6fS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 赚了一头牛', 'NewMsgId': 7744908194717853046, 'MsgSeq': 871416062}
2025-08-01 07:50:08 | INFO | 收到文本消息: 消息ID:1484659106 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:赚了一头牛
2025-08-01 07:50:09 | DEBUG | 处理消息内容: '赚了一头牛'
2025-08-01 07:50:09 | DEBUG | 消息内容 '赚了一头牛' 不匹配任何命令，忽略
2025-08-01 07:50:11 | DEBUG | 收到消息: {'MsgId': 894168641, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'zll953369865:\n<msg><emoji fromusername = "zll953369865" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="3714d6d788bf35119abe94e4f98edd20" len = "511861" productid="com.tencent.xin.emoticon.person.stiker_1556176061bdd89e4e8595e2bb" androidmd5="3714d6d788bf35119abe94e4f98edd20" androidlen="511861" s60v3md5 = "3714d6d788bf35119abe94e4f98edd20" s60v3len="511861" s60v5md5 = "3714d6d788bf35119abe94e4f98edd20" s60v5len="511861" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=3714d6d788bf35119abe94e4f98edd20&amp;filekey=30350201010421301f020201060402534804103714d6d788bf35119abe94e4f98edd20020307cf75040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630ad65e0003fd75000000000000010600004f5053480166fb40b7344c775&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=650c870fdba654e472bc68f2141dccbd&amp;filekey=30340201010420301e02020113040253480410650c870fdba654e472bc68f2141dccbd02026cd7040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479ce4100094ef1000000000000011300004f5053482f3edb01e6f75a64c&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=d182fca2d842a7066fd2add20d1ba66e&amp;filekey=30350201010421301f02020106040253480410d182fca2d842a7066fd2add20d1ba66e020307cf80040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630ad65e00083726000000000000010600004f5053480f267b40b733c3b75&amp;bizid=1023" aeskey= "3217dbdb78389dd1ff77745b999fedb0" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=1e2736ac444e928c5ed737f6aff547e7&amp;filekey=30350201010421301f020201060402534804101e2736ac444e928c5ed737f6aff547e70203010180040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630ad65e000bf6ec000000000000010600004f5053481d264b40b733d253d&amp;bizid=1023" externmd5 = "e88f3bf910109a48ccb6575e142b4b66" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "Cgzlk4jlk4jlk4jlk4g=" linkid= "" desc= "Cg8KBXpoX2NuEgblk4jlk4gKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005816, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_7nCITn37|v1_L8eNs3CI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包在群聊中发了一个表情', 'NewMsgId': 7073784744058514347, 'MsgSeq': 871416063}
2025-08-01 07:50:11 | INFO | 收到表情消息: 消息ID:894168641 来自:48097389945@chatroom 发送人:zll953369865 MD5:3714d6d788bf35119abe94e4f98edd20 大小:511861
2025-08-01 07:50:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7073784744058514347
2025-08-01 07:50:51 | DEBUG | 收到消息: {'MsgId': 640506460, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_0o755tqnn7p22:\n唱舞签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754005857, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_X4m24oMO|v1_72kPIp91</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3275095969990644140, 'MsgSeq': 871416064}
2025-08-01 07:50:51 | INFO | 收到文本消息: 消息ID:640506460 来自:27852221909@chatroom 发送人:wxid_0o755tqnn7p22 @:[] 内容:唱舞签到
2025-08-01 07:50:52 | INFO | 发送链接消息: 对方wxid:27852221909@chatroom 链接:https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect 标题:唱舞全明星 描述:点击进入签到页面，领取专属福利 缩略图链接:https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0
2025-08-01 07:50:52 | DEBUG | 处理消息内容: '唱舞签到'
2025-08-01 07:50:52 | DEBUG | 消息内容 '唱舞签到' 不匹配任何命令，忽略
2025-08-01 07:54:56 | DEBUG | 收到消息: {'MsgId': 117688320, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006101, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_M5OeGqmL|v1_cuWif626</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6313038335473116726, 'MsgSeq': 871416067}
2025-08-01 07:54:56 | INFO | 收到文本消息: 消息ID:117688320 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:签到
2025-08-01 07:54:56 | DEBUG | 处理消息内容: '签到'
2025-08-01 07:54:56 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-08-01 07:54:56 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-01 07:54:56 | INFO | 数据库: 用户qianting1731076232登录时间设置为2025-08-01 00:00:00+08:00
2025-08-01 07:54:56 | INFO | 数据库: 用户qianting1731076232连续签到天数设置为9
2025-08-01 07:54:56 | INFO | 数据库: 用户qianting1731076232积分增加12
2025-08-01 07:54:57 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['qianting1731076232'] 内容:@奈斯༩༧ 
-----XYBot-----
签到成功！你领到了 11 个积分！✅
你是今天第 2 个签到的！🎉
你连续签到了 9 天！ 再奖励 1 积分！[爱心]
2025-08-01 08:00:00 | INFO | [QuarkSignIn] 开始执行定时自动签到任务
2025-08-01 08:00:00 | DEBUG | [QuarkSignIn] API响应状态码: 200
2025-08-01 08:00:01 | DEBUG | [QuarkSignIn] 签到响应状态码: 200
2025-08-01 08:00:01 | INFO | 发送文字消息: 对方wxid:wxid_ubbh6q832tcs21 at: 内容:🤖 夸克网盘自动签到完成

🌟 夸克网盘签到开始
📊 检测到 1 个账号

🔄 第1个账号签到中...
👤 普通用户 夸父4527
💾 网盘总容量：19.95 GB
📈 签到累计容量：5.95 GB
🎉 今日签到成功+40.00 MB，连签进度(2/7)

✨ 夸克网盘签到完成
2025-08-01 08:00:01 | INFO | [QuarkSignIn] 已发送签到通知到: wxid_ubbh6q832tcs21
2025-08-01 08:00:04 | DEBUG | 收到消息: {'MsgId': 1719396238, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>哈哈外卖是不是没有想象中跑跑就能赚很多钱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4977536727846509900</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_jegyk4i3v7zg22</chatusr>\n\t\t\t<displayname>阿尼亚与她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;alnode&gt;\n\t\t&lt;inlenlist&gt;62&lt;/inlenlist&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_eOr2xjqD|v1_DWtUzE67&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n本来暑假前想的很好，结果暑假工被骗了1000，驾照还挂了，送个外卖两天挣80。现在啥也不干了，醒了玩饿了吃困了睡。</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754004134</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_n5c0aekjceu621</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006409, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>f723c5384a11ca8f2be19b02c3c612a2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_ltIcRcFf|v1_aqfel2YP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 哈哈外卖是不是没有想象中跑跑就能赚很多钱', 'NewMsgId': 3302060468344220883, 'MsgSeq': 871416074}
2025-08-01 08:00:04 | DEBUG | 从群聊消息中提取发送者: wxid_n5c0aekjceu621
2025-08-01 08:00:04 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 08:00:04 | INFO | 收到引用消息: 消息ID:1719396238 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 内容:哈哈外卖是不是没有想象中跑跑就能赚很多钱 引用类型:1
2025-08-01 08:00:04 | INFO | [DouBaoImageToImage] 收到引用消息: 哈哈外卖是不是没有想象中跑跑就能赚很多钱
2025-08-01 08:00:04 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 08:00:04 | INFO |   - 消息内容: 哈哈外卖是不是没有想象中跑跑就能赚很多钱
2025-08-01 08:00:04 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 08:00:04 | INFO |   - 发送人: wxid_n5c0aekjceu621
2025-08-01 08:00:04 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n本来暑假前想的很好，结果暑假工被骗了1000，驾照还挂了，送个外卖两天挣80。现在啥也不干了，醒了玩饿了吃困了睡。', 'Msgid': '4977536727846509900', 'NewMsgId': '4977536727846509900', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '阿尼亚与她', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>62</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_eOr2xjqD|v1_DWtUzE67</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754004134', 'SenderWxid': 'wxid_n5c0aekjceu621'}
2025-08-01 08:00:04 | INFO |   - 引用消息ID: 
2025-08-01 08:00:04 | INFO |   - 引用消息类型: 
2025-08-01 08:00:04 | INFO |   - 引用消息内容: 
本来暑假前想的很好，结果暑假工被骗了1000，驾照还挂了，送个外卖两天挣80。现在啥也不干了，醒了玩饿了吃困了睡。
2025-08-01 08:00:04 | INFO |   - 引用消息发送人: wxid_n5c0aekjceu621
2025-08-01 08:00:47 | DEBUG | 收到消息: {'MsgId': 1154023406, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@赵如初\u2005哎呀我换了两个FJ'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006452, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_zbh5p28da1si22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_oAQ8KMDo|v1_TDUO2z1J</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3485612368613092555, 'MsgSeq': 871416075}
2025-08-01 08:00:47 | INFO | 收到文本消息: 消息ID:1154023406 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_zbh5p28da1si22'] 内容:@赵如初 哎呀我换了两个FJ
2025-08-01 08:00:47 | DEBUG | 处理消息内容: '@赵如初 哎呀我换了两个FJ'
2025-08-01 08:00:47 | DEBUG | 消息内容 '@赵如初 哎呀我换了两个FJ' 不匹配任何命令，忽略
2025-08-01 08:01:01 | DEBUG | 收到消息: {'MsgId': 1252897761, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n没事'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006466, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_dvJatfvY|v1_mrkzr4A0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5022156119160230970, 'MsgSeq': 871416076}
2025-08-01 08:01:01 | INFO | 收到文本消息: 消息ID:1252897761 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:没事
2025-08-01 08:01:01 | DEBUG | 处理消息内容: '没事'
2025-08-01 08:01:01 | DEBUG | 消息内容 '没事' 不匹配任何命令，忽略
2025-08-01 08:01:25 | DEBUG | 收到消息: {'MsgId': 1119959403, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n再来出来给你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006491, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_gO86u1Oa|v1_kdBrDeVh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3503781669432911732, 'MsgSeq': 871416077}
2025-08-01 08:01:25 | INFO | 收到文本消息: 消息ID:1119959403 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:再来出来给你
2025-08-01 08:01:25 | DEBUG | 处理消息内容: '再来出来给你'
2025-08-01 08:01:25 | DEBUG | 消息内容 '再来出来给你' 不匹配任何命令，忽略
2025-08-01 08:01:37 | DEBUG | 收到消息: {'MsgId': 1780015531, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n开'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006502, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_63mA7kVI|v1_lx9D5wCO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2649654932419773468, 'MsgSeq': 871416078}
2025-08-01 08:01:37 | INFO | 收到文本消息: 消息ID:1780015531 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:开
2025-08-01 08:01:37 | DEBUG | 处理消息内容: '开'
2025-08-01 08:01:37 | DEBUG | 消息内容 '开' 不匹配任何命令，忽略
2025-08-01 08:04:44 | DEBUG | 收到消息: {'MsgId': 998813552, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n好的，记得给我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006689, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_wEkvJ8wL|v1_vdLh3ypQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6172237681143725446, 'MsgSeq': 871416079}
2025-08-01 08:04:44 | INFO | 收到文本消息: 消息ID:998813552 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:好的，记得给我
2025-08-01 08:04:44 | DEBUG | 处理消息内容: '好的，记得给我'
2025-08-01 08:04:44 | DEBUG | 消息内容 '好的，记得给我' 不匹配任何命令，忽略
2025-08-01 08:05:46 | DEBUG | 收到消息: {'MsgId': 789766201, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="d884353358b361d736650d0c545aac07" len="50865" productid="" androidmd5="d884353358b361d736650d0c545aac07" androidlen="50865" s60v3md5="d884353358b361d736650d0c545aac07" s60v3len="50865" s60v5md5="d884353358b361d736650d0c545aac07" s60v5len="50865" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=d884353358b361d736650d0c545aac07&amp;filekey=30440201010430302e02016e0402534804206438383433353333353862333631643733363635306430633534356161633037020300c6b1040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000bfe256c8a355e0000006e01004fb153481843603156573f36c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=f43b05c3968c31b7d82993757e59a91d&amp;filekey=30440201010430302e02016e0402534804206634336230356333393638633331623764383239393337353765353961393164020300c6c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000c83936c8a355e0000006e02004fb253481843603156573f382&amp;ef=2&amp;bizid=1022" aeskey="119a7327626d4b24b7c42d52abb96865" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=5b407b405193abd441e88f6d3b0b34dd&amp;filekey=3043020101042f302d02016e040253480420356234303762343035313933616264343431653838663664336230623334646402021080040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000e1e096c8a355e0000006e03004fb353481843603156573f3a8&amp;ef=3&amp;bizid=1022" externmd5="ac72583a1d0162cd2943243c3e15f83c" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006751, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_QHZxCmao|v1_RvDy9fYr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 3107086901443993505, 'MsgSeq': 871416080}
2025-08-01 08:05:46 | INFO | 收到表情消息: 消息ID:789766201 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:d884353358b361d736650d0c545aac07 大小:50865
2025-08-01 08:05:47 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3107086901443993505
2025-08-01 08:07:21 | DEBUG | 收到消息: {'MsgId': 451279427, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_2530z9t0joek22:\n<msg><emoji fromusername = "wxid_2530z9t0joek22" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="f153a290ac448152bd7c7a4846b1d482" len = "351499" productid="" androidmd5="f153a290ac448152bd7c7a4846b1d482" androidlen="351499" s60v3md5 = "f153a290ac448152bd7c7a4846b1d482" s60v3len="351499" s60v5md5 = "f153a290ac448152bd7c7a4846b1d482" s60v5len="351499" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=f153a290ac448152bd7c7a4846b1d482&amp;filekey=30440201010430302e02016e0402535a042066313533613239306163343438313532626437633761343834366231643438320203055d0b040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2631eb4c5000e7ce31fcdafa60000006e01004fb1535a28f87910b6c91e16d&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=ab3098367206f8554fc5dc62ce939145&amp;filekey=30440201010430302e02016e0402535a042061623330393833363732303666383535346663356463363263653933393134350203055d10040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2631eb4c600001acb1fcdafa60000006e02004fb2535a28f87910b6c91e18a&amp;ef=2&amp;bizid=1022" aeskey= "aa831e31fa094c4695af2b9570f1bde0" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=560aeae793c258d25109445702cffc13&amp;filekey=3043020101042f302d02016e0402535a0420353630616561653739336332353864323531303934343537303263666663313302026f40040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2631eb4c60001059c1fcdafa60000006e03004fb3535a28f87910b6c91e1a5&amp;ef=3&amp;bizid=1022" externmd5 = "6639370c384040feb31d2f417e69aa23" width= "613" height= "616" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754006846, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_CaC5ShEJ|v1_bo8cCuYz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6256155348746329848, 'MsgSeq': 871416081}
2025-08-01 08:07:21 | INFO | 收到表情消息: 消息ID:451279427 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 MD5:f153a290ac448152bd7c7a4846b1d482 大小:351499
2025-08-01 08:07:21 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6256155348746329848
2025-08-01 08:12:03 | DEBUG | 收到消息: {'MsgId': 1537811609, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="f92d3660a9a5683695cd7d2df55eb851" encryver="1" cdnthumbaeskey="f92d3660a9a5683695cd7d2df55eb851" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c0657042464653061333261612d323037362d343035352d386137332d306339393661353464373834020405250a020201000405004c543d00" cdnthumblength="5919" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c0657042464653061333261612d323037362d343035352d386137332d306339393661353464373834020405250a020201000405004c543d00" length="231532" md5="2e3b3442e5db1c2d9c7f382b0bf8e5e7" originsourcemd5="5f185a1ad05d660ea5e2c25b04422770">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007127, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>5922b41dde59342bd2e9bca2cfe383a9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_MTuLCOZ9|v1_Xc7SFgU3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 2627329508339125209, 'MsgSeq': 871416082}
2025-08-01 08:12:03 | INFO | 收到图片消息: 消息ID:1537811609 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="f92d3660a9a5683695cd7d2df55eb851" encryver="1" cdnthumbaeskey="f92d3660a9a5683695cd7d2df55eb851" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c0657042464653061333261612d323037362d343035352d386137332d306339393661353464373834020405250a020201000405004c543d00" cdnthumblength="5919" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c0657042464653061333261612d323037362d343035352d386137332d306339393661353464373834020405250a020201000405004c543d00" length="231532" md5="2e3b3442e5db1c2d9c7f382b0bf8e5e7" originsourcemd5="5f185a1ad05d660ea5e2c25b04422770"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 08:12:03 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-01 08:12:03 | INFO | [TimerTask] 缓存图片消息: 1537811609
2025-08-01 08:12:47 | DEBUG | 收到消息: {'MsgId': 749298389, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n可灵 生成真人实拍，绝色容颜，朦胧的轮廓剪影，水汪汪的眼睛，嘴唇丰润，发丝灵动，羞涩，细节超细腻，32K高清，背着一个精致包，大长腿，站在商场的楼梯间，白色公主风超短裙，高跟鞋，超低仰角视角，写实风格，细节丰富细腻，时尚潮流感，超近镜头拍摄！超低机位从地面向上拍摄全身，从下往上拍，人物主体超近景镜头！背对着，在高高的扶梯上蓦然回首。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007172, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>167</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_E07c1YiW|v1_Jrhkl2ZE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 可灵 生成真人实拍，绝色容颜，朦胧的轮廓剪影，水汪汪的眼睛，...', 'NewMsgId': 1127155435084319893, 'MsgSeq': 871416083}
2025-08-01 08:12:47 | INFO | 收到文本消息: 消息ID:749298389 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:可灵 生成真人实拍，绝色容颜，朦胧的轮廓剪影，水汪汪的眼睛，嘴唇丰润，发丝灵动，羞涩，细节超细腻，32K高清，背着一个精致包，大长腿，站在商场的楼梯间，白色公主风超短裙，高跟鞋，超低仰角视角，写实风格，细节丰富细腻，时尚潮流感，超近镜头拍摄！超低机位从地面向上拍摄全身，从下往上拍，人物主体超近景镜头！背对着，在高高的扶梯上蓦然回首。
2025-08-01 08:12:47 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-01 08:13:10 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-08-01 08:13:10 | DEBUG | 处理消息内容: '可灵 生成真人实拍，绝色容颜，朦胧的轮廓剪影，水汪汪的眼睛，嘴唇丰润，发丝灵动，羞涩，细节超细腻，32K高清，背着一个精致包，大长腿，站在商场的楼梯间，白色公主风超短裙，高跟鞋，超低仰角视角，写实风格，细节丰富细腻，时尚潮流感，超近镜头拍摄！超低机位从地面向上拍摄全身，从下往上拍，人物主体超近景镜头！背对着，在高高的扶梯上蓦然回首。'
2025-08-01 08:13:10 | DEBUG | 消息内容 '可灵 生成真人实拍，绝色容颜，朦胧的轮廓剪影，水汪汪的眼睛，嘴唇丰润，发丝灵动，羞涩，细节超细腻，32K高清，背着一个精致包，大长腿，站在商场的楼梯间，白色公主风超短裙，高跟鞋，超低仰角视角，写实风格，细节丰富细腻，时尚潮流感，超近镜头拍摄！超低机位从地面向上拍摄全身，从下往上拍，人物主体超近景镜头！背对着，在高高的扶梯上蓦然回首。' 不匹配任何命令，忽略
2025-08-01 08:14:25 | DEBUG | 收到消息: {'MsgId': 1625923875, 'FromUserName': {'string': '43607022446@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="c0f082662f3979e6c0febfe16f528f7c" encryver="1" cdnthumbaeskey="c0f082662f3979e6c0febfe16f528f7c" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c06e5042435633432313937352d373932622d343335632d613435662d333966393339643138656266020405250a020201000405004c543d00" cdnthumblength="5919" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c06e5042435633432313937352d373932622d343335632d613435662d333966393339643138656266020405250a020201000405004c543d00" length="231532" md5="2e3b3442e5db1c2d9c7f382b0bf8e5e7" originsourcemd5="5f185a1ad05d660ea5e2c25b04422770">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007269, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>379834e9a7aa07de181dcb6d74c4e6d2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>76</membercount>\n\t<signature>N0_V1_5CWnGDSN|v1_OJxmSx66</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '吃瓜小爱\ue124\ue124\u2005在群聊中发了一张图片', 'NewMsgId': 7131216899244986313, 'MsgSeq': 871416088}
2025-08-01 08:14:25 | INFO | 收到图片消息: 消息ID:1625923875 来自:43607022446@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="c0f082662f3979e6c0febfe16f528f7c" encryver="1" cdnthumbaeskey="c0f082662f3979e6c0febfe16f528f7c" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c06e5042435633432313937352d373932622d343335632d613435662d333966393339643138656266020405250a020201000405004c543d00" cdnthumblength="5919" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204df3122750204688c06e5042435633432313937352d373932622d343335632d613435662d333966393339643138656266020405250a020201000405004c543d00" length="231532" md5="2e3b3442e5db1c2d9c7f382b0bf8e5e7" originsourcemd5="5f185a1ad05d660ea5e2c25b04422770"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-01 08:14:25 | INFO | [ImageEcho] 保存图片信息成功，当前群 43607022446@chatroom 已存储 5 张图片
2025-08-01 08:14:25 | INFO | [TimerTask] 缓存图片消息: 1625923875
2025-08-01 08:16:07 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-01 08:18:56 | DEBUG | 收到消息: {'MsgId': 1973587825, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>谁呀</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4977536727846509900</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_jegyk4i3v7zg22</chatusr>\n\t\t\t<displayname>阿尼亚与她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;alnode&gt;\n\t\t&lt;inlenlist&gt;62&lt;/inlenlist&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_HSrazSF1|v1_lKKI3LsH&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n本来暑假前想的很好，结果暑假工被骗了1000，驾照还挂了，送个外卖两天挣80。现在啥也不干了，醒了玩饿了吃困了睡。</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754004134</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007540, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6c3f8bdbaa4aabf8f1b12fda1554b5a6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_2XDUk49U|v1_wGE3U55M</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 谁呀', 'NewMsgId': 7540850829333219816, 'MsgSeq': 871416089}
2025-08-01 08:18:56 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-08-01 08:18:56 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 08:18:56 | INFO | 收到引用消息: 消息ID:1973587825 来自:48097389945@chatroom 发送人:xiaomaochong 内容:谁呀 引用类型:1
2025-08-01 08:18:56 | INFO | [DouBaoImageToImage] 收到引用消息: 谁呀
2025-08-01 08:18:56 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 08:18:56 | INFO |   - 消息内容: 谁呀
2025-08-01 08:18:56 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 08:18:56 | INFO |   - 发送人: xiaomaochong
2025-08-01 08:18:56 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n本来暑假前想的很好，结果暑假工被骗了1000，驾照还挂了，送个外卖两天挣80。现在啥也不干了，醒了玩饿了吃困了睡。', 'Msgid': '4977536727846509900', 'NewMsgId': '4977536727846509900', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '阿尼亚与她', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>62</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_HSrazSF1|v1_lKKI3LsH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754004134', 'SenderWxid': 'xiaomaochong'}
2025-08-01 08:18:56 | INFO |   - 引用消息ID: 
2025-08-01 08:18:56 | INFO |   - 引用消息类型: 
2025-08-01 08:18:56 | INFO |   - 引用消息内容: 
本来暑假前想的很好，结果暑假工被骗了1000，驾照还挂了，送个外卖两天挣80。现在啥也不干了，醒了玩饿了吃困了睡。
2025-08-01 08:18:56 | INFO |   - 引用消息发送人: xiaomaochong
2025-08-01 08:19:33 | DEBUG | 收到消息: {'MsgId': 1369480813, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@赵如初\u2005加好友'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007577, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_zbh5p28da1si22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_KAmhgPKH|v1_5zN7NmKS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7043776366775894087, 'MsgSeq': 871416090}
2025-08-01 08:19:33 | INFO | 收到文本消息: 消息ID:1369480813 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_zbh5p28da1si22'] 内容:@赵如初 加好友
2025-08-01 08:19:33 | DEBUG | 处理消息内容: '@赵如初 加好友'
2025-08-01 08:19:33 | DEBUG | 消息内容 '@赵如初 加好友' 不匹配任何命令，忽略
2025-08-01 08:19:48 | DEBUG | 收到消息: {'MsgId': 849061502, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n游戏好友都没'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007593, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_E2nc5O4F|v1_nCkVOETg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6457180612054151807, 'MsgSeq': 871416091}
2025-08-01 08:19:48 | INFO | 收到文本消息: 消息ID:849061502 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:游戏好友都没
2025-08-01 08:19:48 | DEBUG | 处理消息内容: '游戏好友都没'
2025-08-01 08:19:48 | DEBUG | 消息内容 '游戏好友都没' 不匹配任何命令，忽略
2025-08-01 08:20:36 | DEBUG | 收到消息: {'MsgId': 2139301191, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007642, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_sR5EG3YH|v1_3HHThTe2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 洞房', 'NewMsgId': 2946213638991507942, 'MsgSeq': 871416092}
2025-08-01 08:20:36 | INFO | 收到文本消息: 消息ID:2139301191 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:洞房
2025-08-01 08:20:37 | DEBUG | 处理消息内容: '洞房'
2025-08-01 08:20:37 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-08-01 08:20:39 | DEBUG | 收到消息: {'MsgId': 1123166272, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n💕 [锦岚]👩\u200d❤\u200d👨[小爱]💕锦岚你在成人用品店给你最爱的小爱[666]买了电动玩具！\n 💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：玩具夫付\n⛺地点：阳台\n😍活动：无数次\n😘结果：成功\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力: +30\n[玫瑰]恩爱: +1 \n🕒下次:2025-08-01 08:40:44'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007643, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_k/rnf49c|v1_GJM8Leao</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : \ue327 [锦岚]\ue005\u200d\ue022\u200d\ue004[小爱]\ue327锦岚你在成人用品店给你最爱的小爱...', 'NewMsgId': 1496941599411572690, 'MsgSeq': 871416093}
2025-08-01 08:20:39 | INFO | 收到文本消息: 消息ID:1123166272 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:💕 [锦岚]👩‍❤‍👨[小爱]💕锦岚你在成人用品店给你最爱的小爱[666]买了电动玩具！
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：玩具夫付
⛺地点：阳台
😍活动：无数次
😘结果：成功
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +30
[玫瑰]恩爱: +1 
🕒下次:2025-08-01 08:40:44
2025-08-01 08:20:39 | DEBUG | 处理消息内容: '💕 [锦岚]👩‍❤‍👨[小爱]💕锦岚你在成人用品店给你最爱的小爱[666]买了电动玩具！
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：玩具夫付
⛺地点：阳台
😍活动：无数次
😘结果：成功
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +30
[玫瑰]恩爱: +1 
🕒下次:2025-08-01 08:40:44'
2025-08-01 08:20:39 | DEBUG | 消息内容 '💕 [锦岚]👩‍❤‍👨[小爱]💕锦岚你在成人用品店给你最爱的小爱[666]买了电动玩具！
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：玩具夫付
⛺地点：阳台
😍活动：无数次
😘结果：成功
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +30
[玫瑰]恩爱: +1 
🕒下次:2025-08-01 08:40:44' 不匹配任何命令，忽略
2025-08-01 08:20:41 | DEBUG | 收到消息: {'MsgId': 1679058993, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「锦岚」[爱心]「小爱」\n地点：操场\n活动：啪啪\n结果：失败\n羞羞：tmd，竟然到不了底~\n恩爱值减少100\n\n下次:2025-08-01 08:30:42'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007643, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_36Ssh/7+|v1_5QpGZETI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「锦岚」[爱心]「小爱」\n地点：操场\n活动：啪啪\n结果：失败\n羞羞：t...', 'NewMsgId': 7624864829976029806, 'MsgSeq': 871416094}
2025-08-01 08:20:41 | INFO | 收到文本消息: 消息ID:1679058993 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「锦岚」[爱心]「小爱」
地点：操场
活动：啪啪
结果：失败
羞羞：tmd，竟然到不了底~
恩爱值减少100

下次:2025-08-01 08:30:42
2025-08-01 08:20:41 | DEBUG | 处理消息内容: '「锦岚」[爱心]「小爱」
地点：操场
活动：啪啪
结果：失败
羞羞：tmd，竟然到不了底~
恩爱值减少100

下次:2025-08-01 08:30:42'
2025-08-01 08:20:41 | DEBUG | 消息内容 '「锦岚」[爱心]「小爱」
地点：操场
活动：啪啪
结果：失败
羞羞：tmd，竟然到不了底~
恩爱值减少100

下次:2025-08-01 08:30:42' 不匹配任何命令，忽略
2025-08-01 08:20:48 | DEBUG | 收到消息: {'MsgId': 786547555, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n早早早'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007653, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_hfC7imR1|v1_ZE0it6ZV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 早早早', 'NewMsgId': 5553345849719364612, 'MsgSeq': 871416095}
2025-08-01 08:20:48 | INFO | 收到文本消息: 消息ID:786547555 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:早早早
2025-08-01 08:20:48 | DEBUG | 处理消息内容: '早早早'
2025-08-01 08:20:48 | DEBUG | 消息内容 '早早早' 不匹配任何命令，忽略
2025-08-01 08:22:23 | DEBUG | 收到消息: {'MsgId': 765828478, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14713243301375773099</objectId>\n\t\t\t<objectNonceId>9921416983139705193_4_20_13_1_1754007663907186_68eb0eb6-6e6d-11f0-87cc-ffc8937fa72c</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>茶馆小娱</nickname>\n\t\t\t<username>v2_060000231003b20faec8c7e18f1dc7d1cb01ed30b077f875ce3add9a2b06d12ca38b721e268d@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/lVakcbdAn2uxlcS4UH5M5kt3vsphicBicKCEMWFGl5J8RdD3GkKAOWjBibt2gMhdA2Cgxs3I0yU6lDVlZdSmyVkic3pgBdZr50qUWJqrOzLCt8r0w6x0IricUBRdhIrqYDhGm/0]]></avatar>\n\t\t\t<desc>原来蛇真的有……小说妹多年的疑问终于解开了！#小说#蛇#宠物#猪鼻蛇#小说妹</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQB1eGhxL4FY4VoOSYDxueEvN4DlXqiao5oAlzJZyqh1FwkGo0zaIUTWcGp1icsprSHEnicMTMp6e4o8y5V4UxGcRSS&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a21a&token=cztXnd9GyrHCnaWppp6HpO8Ribky9HQdyfHkHIuxnSOWkvTowLD728fmoNCOJ0Pbqjcr4uia6QDhOUjylkKTPL9JVmGKNia5tF0ciay95oLZzFehQmibZUibLD6GmofzhOoibpemvheUk8LW0nbDNHTmR716ppl3pvQdy3jPR0wVObkYib0&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTI3GgZ4V1QxMTMaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=mjYse2-By2HbLAy9hMOP72PhGZip_OYYvb0mSyh2HYCqZfeQaGXuS9BZ2YMmxb6BriRH5z8pyL3k9MAMaMSy9A&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAACRRWBcQkBQW5J8cgiMaBAAAADnaHZTnGbFfAj9RgZXfw6VQC1nDO6xZsbRifCRR2IWbxkiprtFzj5fbzSoWrOrD2K3NwJPRIN64jQ%3D&svrnonce=1754007666]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzXQibBrL1tIwfcKpgGbVjsWbubmVlib3jHlfLWWazppbeCFEV21ZZtB7AjsiaRcROVeqfS1uYbwIbZBwticgyMOEhbA&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxqSktaIZ7RVtkgx4ImFWiaMic4bCUsavdVp2NSr7MQkFaWLwyMmHa2rIzOh4WjVAWKqIRb0KZnqTPiaQACTEia1kqAcc47JPhn00hDHD2okCGEFmYHibrR0M7t8kr17m2cXGS3vO312GPncicq&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzXQibBrL1tIwfcKpgGbVjsWbubmVlib3jHlfLWWazppbeCFEV21ZZtB7AjsiaRcROVeqfS1uYbwIbZBwticgyMOEhbA&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxqSktaIZ7RVtkgx4ImFWiaMic4bCUsavdVp2NSr7MQkFaWLwyMmHa2rIzOh4WjVAWKqIRb0KZnqTPiaQACTEia1kqAcc47JPhn00hDHD2okCGEFmYHibrR0M7t8kr17m2cXGS3vO312GPncicq&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>22</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754007723675","contextId":"1-1-20-13e85f443a7340b2ab2b68eb8752e52e","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007748, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>e119ea28b7b2e14216973af2dcfb4523_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_NMyL2S1G|v1_UT1zrPAT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 7896559669245591331, 'MsgSeq': 871416096}
2025-08-01 08:22:23 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-08-01 08:22:23 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713243301375773099</objectId>
			<objectNonceId>9921416983139705193_4_20_13_1_1754007663907186_68eb0eb6-6e6d-11f0-87cc-ffc8937fa72c</objectNonceId>
			<feedType>4</feedType>
			<nickname>茶馆小娱</nickname>
			<username>v2_060000231003b20faec8c7e18f1dc7d1cb01ed30b077f875ce3add9a2b06d12ca38b721e268d@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/lVakcbdAn2uxlcS4UH5M5kt3vsphicBicKCEMWFGl5J8RdD3GkKAOWjBibt2gMhdA2Cgxs3I0yU6lDVlZdSmyVkic3pgBdZr50qUWJqrOzLCt8r0w6x0IricUBRdhIrqYDhGm/0]]></avatar>
			<desc>原来蛇真的有……小说妹多年的疑问终于解开了！#小说#蛇#宠物#猪鼻蛇#小说妹</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQB1eGhxL4FY4VoOSYDxueEvN4DlXqiao5oAlzJZyqh1FwkGo0zaIUTWcGp1icsprSHEnicMTMp6e4o8y5V4UxGcRSS&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a21a&token=cztXnd9GyrHCnaWppp6HpO8Ribky9HQdyfHkHIuxnSOWkvTowLD728fmoNCOJ0Pbqjcr4uia6QDhOUjylkKTPL9JVmGKNia5tF0ciay95oLZzFehQmibZUibLD6GmofzhOoibpemvheUk8LW0nbDNHTmR716ppl3pvQdy3jPR0wVObkYib0&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTI3GgZ4V1QxMTMaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=mjYse2-By2HbLAy9hMOP72PhGZip_OYYvb0mSyh2HYCqZfeQaGXuS9BZ2YMmxb6BriRH5z8pyL3k9MAMaMSy9A&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAACRRWBcQkBQW5J8cgiMaBAAAADnaHZTnGbFfAj9RgZXfw6VQC1nDO6xZsbRifCRR2IWbxkiprtFzj5fbzSoWrOrD2K3NwJPRIN64jQ%3D&svrnonce=1754007666]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzXQibBrL1tIwfcKpgGbVjsWbubmVlib3jHlfLWWazppbeCFEV21ZZtB7AjsiaRcROVeqfS1uYbwIbZBwticgyMOEhbA&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxqSktaIZ7RVtkgx4ImFWiaMic4bCUsavdVp2NSr7MQkFaWLwyMmHa2rIzOh4WjVAWKqIRb0KZnqTPiaQACTEia1kqAcc47JPhn00hDHD2okCGEFmYHibrR0M7t8kr17m2cXGS3vO312GPncicq&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzXQibBrL1tIwfcKpgGbVjsWbubmVlib3jHlfLWWazppbeCFEV21ZZtB7AjsiaRcROVeqfS1uYbwIbZBwticgyMOEhbA&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxqSktaIZ7RVtkgx4ImFWiaMic4bCUsavdVp2NSr7MQkFaWLwyMmHa2rIzOh4WjVAWKqIRb0KZnqTPiaQACTEia1kqAcc47JPhn00hDHD2okCGEFmYHibrR0M7t8kr17m2cXGS3vO312GPncicq&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>22</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754007723675","contextId":"1-1-20-13e85f443a7340b2ab2b68eb8752e52e","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 08:22:23 | DEBUG | XML消息类型: 51
2025-08-01 08:22:23 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-01 08:22:23 | DEBUG | XML消息描述: None
2025-08-01 08:22:23 | DEBUG | 附件信息 totallen: 0
2025-08-01 08:22:23 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-01 08:22:23 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-01 08:22:23 | INFO | 未知的XML消息类型: 51
2025-08-01 08:22:23 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-01 08:22:23 | INFO | 消息描述: None
2025-08-01 08:22:23 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-01 08:22:23 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713243301375773099</objectId>
			<objectNonceId>9921416983139705193_4_20_13_1_1754007663907186_68eb0eb6-6e6d-11f0-87cc-ffc8937fa72c</objectNonceId>
			<feedType>4</feedType>
			<nickname>茶馆小娱</nickname>
			<username>v2_060000231003b20faec8c7e18f1dc7d1cb01ed30b077f875ce3add9a2b06d12ca38b721e268d@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/lVakcbdAn2uxlcS4UH5M5kt3vsphicBicKCEMWFGl5J8RdD3GkKAOWjBibt2gMhdA2Cgxs3I0yU6lDVlZdSmyVkic3pgBdZr50qUWJqrOzLCt8r0w6x0IricUBRdhIrqYDhGm/0]]></avatar>
			<desc>原来蛇真的有……小说妹多年的疑问终于解开了！#小说#蛇#宠物#猪鼻蛇#小说妹</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQB1eGhxL4FY4VoOSYDxueEvN4DlXqiao5oAlzJZyqh1FwkGo0zaIUTWcGp1icsprSHEnicMTMp6e4o8y5V4UxGcRSS&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a21a&token=cztXnd9GyrHCnaWppp6HpO8Ribky9HQdyfHkHIuxnSOWkvTowLD728fmoNCOJ0Pbqjcr4uia6QDhOUjylkKTPL9JVmGKNia5tF0ciay95oLZzFehQmibZUibLD6GmofzhOoibpemvheUk8LW0nbDNHTmR716ppl3pvQdy3jPR0wVObkYib0&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTI3GgZ4V1QxMTMaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=mjYse2-By2HbLAy9hMOP72PhGZip_OYYvb0mSyh2HYCqZfeQaGXuS9BZ2YMmxb6BriRH5z8pyL3k9MAMaMSy9A&ctsc=20&extg=108bd00&svrbypass=AAuL%2FQsFAAABAAAAAACRRWBcQkBQW5J8cgiMaBAAAADnaHZTnGbFfAj9RgZXfw6VQC1nDO6xZsbRifCRR2IWbxkiprtFzj5fbzSoWrOrD2K3NwJPRIN64jQ%3D&svrnonce=1754007666]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzXQibBrL1tIwfcKpgGbVjsWbubmVlib3jHlfLWWazppbeCFEV21ZZtB7AjsiaRcROVeqfS1uYbwIbZBwticgyMOEhbA&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxqSktaIZ7RVtkgx4ImFWiaMic4bCUsavdVp2NSr7MQkFaWLwyMmHa2rIzOh4WjVAWKqIRb0KZnqTPiaQACTEia1kqAcc47JPhn00hDHD2okCGEFmYHibrR0M7t8kr17m2cXGS3vO312GPncicq&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzXQibBrL1tIwfcKpgGbVjsWbubmVlib3jHlfLWWazppbeCFEV21ZZtB7AjsiaRcROVeqfS1uYbwIbZBwticgyMOEhbA&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxqSktaIZ7RVtkgx4ImFWiaMic4bCUsavdVp2NSr7MQkFaWLwyMmHa2rIzOh4WjVAWKqIRb0KZnqTPiaQACTEia1kqAcc47JPhn00hDHD2okCGEFmYHibrR0M7t8kr17m2cXGS3vO312GPncicq&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>22</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754007723675","contextId":"1-1-20-13e85f443a7340b2ab2b68eb8752e52e","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-08-01 08:22:56 | DEBUG | 收到消息: {'MsgId': 1492694003, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n@慕ؓ悦ؓ˒\u2005你加我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007781, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_2530z9t0joek22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_pftHFTWt|v1_TrkpYu3G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6333725201006584314, 'MsgSeq': 871416097}
2025-08-01 08:22:56 | INFO | 收到文本消息: 消息ID:1492694003 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:['wxid_2530z9t0joek22'] 内容:@慕ؓ悦ؓ˒ 你加我
2025-08-01 08:22:56 | DEBUG | 处理消息内容: '@慕ؓ悦ؓ˒ 你加我'
2025-08-01 08:22:56 | DEBUG | 消息内容 '@慕ؓ悦ؓ˒ 你加我' 不匹配任何命令，忽略
2025-08-01 08:23:01 | DEBUG | 收到消息: {'MsgId': 231309943, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n就叫这个名字'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007786, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_YoejDi9V|v1_PNHmsQGz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 20650600115330027, 'MsgSeq': 871416098}
2025-08-01 08:23:01 | INFO | 收到文本消息: 消息ID:231309943 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:就叫这个名字
2025-08-01 08:23:01 | DEBUG | 处理消息内容: '就叫这个名字'
2025-08-01 08:23:01 | DEBUG | 消息内容 '就叫这个名字' 不匹配任何命令，忽略
2025-08-01 08:23:05 | DEBUG | 收到消息: {'MsgId': 1509622949, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n我等会上'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007790, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_uew4hpwt|v1_F/l3bUfY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1451228177343656694, 'MsgSeq': 871416099}
2025-08-01 08:23:05 | INFO | 收到文本消息: 消息ID:1509622949 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:我等会上
2025-08-01 08:23:05 | DEBUG | 处理消息内容: '我等会上'
2025-08-01 08:23:05 | DEBUG | 消息内容 '我等会上' 不匹配任何命令，忽略
2025-08-01 08:23:37 | DEBUG | 收到消息: {'MsgId': 388752347, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_besewpsontwy29:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754007823, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_ct0ag2Xv|v1_4i1pF3hM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3768987171423814998, 'MsgSeq': 871416100}
2025-08-01 08:23:37 | INFO | 收到文本消息: 消息ID:388752347 来自:27852221909@chatroom 发送人:wxid_besewpsontwy29 @:[] 内容:签到
2025-08-01 08:23:37 | DEBUG | 处理消息内容: '签到'
2025-08-01 08:23:37 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-08-01 08:23:38 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-01 08:23:38 | INFO | 数据库: 用户wxid_besewpsontwy29登录时间设置为2025-08-01 00:00:00+08:00
2025-08-01 08:23:38 | INFO | 数据库: 用户wxid_besewpsontwy29连续签到天数设置为11
2025-08-01 08:23:38 | INFO | 数据库: 用户wxid_besewpsontwy29积分增加22
2025-08-01 08:23:39 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['wxid_besewpsontwy29'] 内容:@穆穆 
-----XYBot-----
签到成功！你领到了 20 个积分！✅
你是今天第 3 个签到的！🎉
你连续签到了 11 天！ 再奖励 2 积分！[爱心]
2025-08-01 08:26:38 | DEBUG | 收到消息: {'MsgId': 600640893, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n@慕ؓ悦ؓ˒\u2005最后一张送我一个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008003, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_2530z9t0joek22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_LAzNNkuD|v1_Fg5sbf4m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 220229053480932577, 'MsgSeq': 871416105}
2025-08-01 08:26:38 | INFO | 收到文本消息: 消息ID:600640893 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:['wxid_2530z9t0joek22'] 内容:@慕ؓ悦ؓ˒ 最后一张送我一个
2025-08-01 08:26:38 | DEBUG | 处理消息内容: '@慕ؓ悦ؓ˒ 最后一张送我一个'
2025-08-01 08:26:38 | DEBUG | 消息内容 '@慕ؓ悦ؓ˒ 最后一张送我一个' 不匹配任何命令，忽略
2025-08-01 08:26:42 | DEBUG | 收到消息: {'MsgId': 994761588, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n[阴险]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008008, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_h4S8m2z/|v1_QsfydxHx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2185218615569191538, 'MsgSeq': 871416106}
2025-08-01 08:26:42 | INFO | 收到表情消息: 消息ID:994761588 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:[阴险]
2025-08-01 08:26:42 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2185218615569191538
2025-08-01 08:27:10 | DEBUG | 收到消息: {'MsgId': 1327322054, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n温度排行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008035, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_kVLDUsW/|v1_z/0d+6EE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 温度排行', 'NewMsgId': 1657044049343634074, 'MsgSeq': 871416107}
2025-08-01 08:27:10 | INFO | 收到文本消息: 消息ID:1327322054 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:温度排行
2025-08-01 08:27:10 | DEBUG | 处理消息内容: '温度排行'
2025-08-01 08:27:10 | DEBUG | 消息内容 '温度排行' 不匹配任何命令，忽略
2025-08-01 08:27:22 | DEBUG | 收到消息: {'MsgId': 1387448417, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n功能菜单'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008047, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_fVHcdYNQ|v1_5tp0/Vlk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 功能菜单', 'NewMsgId': 6845928340080260921, 'MsgSeq': 871416108}
2025-08-01 08:27:22 | INFO | 收到文本消息: 消息ID:1387448417 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:功能菜单
2025-08-01 08:27:23 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-01 08:27:25 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at:['xiaomaochong'] 内容:@小爱 
-======== XYBot ========-
🤖AI聊天🤖 (可指令可群@可私信语音可私信图片)
☁️天气☁️          🎵点歌🎵
📰新闻📰           ♟️五子棋♟️
📰随机新闻📰    🏞️随机图片🏞️
🔢随机群员🔢    🎮战雷查询🎮

✅签到✅           💰积分查询💰
🏆积分榜🏆       🏆群积分榜🏆
🤝积分交易🤝   🤑积分抽奖🤑
🧧积分红包🧧

⚙️查看管理员菜单请发送：管理员菜单

来自XYBotV2 v1.0.0
https://github.com/HenryXiaoYang/XYBotV2
2025-08-01 08:27:25 | DEBUG | 处理消息内容: '功能菜单'
2025-08-01 08:27:25 | DEBUG | 消息内容 '功能菜单' 不匹配任何命令，忽略
2025-08-01 08:27:25 | DEBUG | 收到消息: {'MsgId': 1776307665, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我呀</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>7540850829333219816</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;appmsg appid=""  sdkver="0"&gt;&lt;title&gt;谁呀&lt;/title&gt;&lt;type&gt;57&lt;/type&gt;&lt;action&gt;view&lt;/action&gt;&lt;appattach&gt;&lt;cdnthumbaeskey&gt;&lt;/cdnthumbaeskey&gt;&lt;aeskey&gt;&lt;/aeskey&gt;&lt;/appattach&gt;&lt;webviewshared&gt;&lt;jsAppId&gt;&lt;![CDATA[]]&gt;&lt;/jsAppId&gt;&lt;/webviewshared&gt;&lt;mpsharetrace&gt;&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;&lt;/mpsharetrace&gt;&lt;secretmsg&gt;&lt;isscrectmsg&gt;0&lt;/isscrectmsg&gt;&lt;/secretmsg&gt;&lt;/appmsg&gt;&lt;fromusername&gt;xiaomaochong&lt;/fromusername&gt;&lt;appinfo&gt;&lt;version&gt;1&lt;/version&gt;&lt;appname&gt;&lt;/appname&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836250642&lt;/sequence_id&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;6c3f8bdbaa4aabf8f1b12fda1554b5a6_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;72&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_U+sdnKfO|v1_5TRGpMbr&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754007540</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_jegyk4i3v7zg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008048, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>57a934e81b34df0940ad0a7fbcdd8a6d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_y9UW3M92|v1_fU5Mowyd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 我呀', 'NewMsgId': 3571670011490501605, 'MsgSeq': 871416109}
2025-08-01 08:27:25 | DEBUG | 从群聊消息中提取发送者: wxid_jegyk4i3v7zg22
2025-08-01 08:27:25 | DEBUG | 使用已解析的XML处理引用消息
2025-08-01 08:27:25 | INFO | 收到引用消息: 消息ID:1776307665 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 内容:我呀 引用类型:49
2025-08-01 08:27:25 | INFO | [DouBaoImageToImage] 收到引用消息: 我呀
2025-08-01 08:27:25 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-01 08:27:25 | INFO |   - 消息内容: 我呀
2025-08-01 08:27:25 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-01 08:27:25 | INFO |   - 发送人: wxid_jegyk4i3v7zg22
2025-08-01 08:27:25 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<msg><appmsg appid=""  sdkver="0"><title>谁呀</title><type>57</type><action>view</action><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><webviewshared><jsAppId><![CDATA[]]></jsAppId></webviewshared><mpsharetrace><hasfinderelement>0</hasfinderelement></mpsharetrace><secretmsg><isscrectmsg>0</isscrectmsg></secretmsg></appmsg><fromusername>xiaomaochong</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>', 'Msgid': '7540850829333219816', 'NewMsgId': '7540850829333219816', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource><sequence_id>836250642</sequence_id>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6c3f8bdbaa4aabf8f1b12fda1554b5a6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_U+sdnKfO|v1_5TRGpMbr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754007540', 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-08-01 08:27:25 | INFO |   - 引用消息ID: 
2025-08-01 08:27:25 | INFO |   - 引用消息类型: 
2025-08-01 08:27:25 | INFO |   - 引用消息内容: <msg><appmsg appid=""  sdkver="0"><title>谁呀</title><type>57</type><action>view</action><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><webviewshared><jsAppId><![CDATA[]]></jsAppId></webviewshared><mpsharetrace><hasfinderelement>0</hasfinderelement></mpsharetrace><secretmsg><isscrectmsg>0</isscrectmsg></secretmsg></appmsg><fromusername>xiaomaochong</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>
2025-08-01 08:27:25 | INFO |   - 引用消息发送人: wxid_jegyk4i3v7zg22
2025-08-01 08:27:26 | DEBUG | 收到消息: {'MsgId': 201284265, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="8a0c6d3e5f8088e644f274ba364fb54d" len="1764845" productid="" androidmd5="8a0c6d3e5f8088e644f274ba364fb54d" androidlen="1764845" s60v3md5="8a0c6d3e5f8088e644f274ba364fb54d" s60v3len="1764845" s60v5md5="8a0c6d3e5f8088e644f274ba364fb54d" s60v5len="1764845" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=8a0c6d3e5f8088e644f274ba364fb54d&amp;filekey=30440201010430302e02016e040253480420386130633664336535663830383865363434663237346261333634666235346402031aeded040d00000004627466730000000132&amp;hy=SH&amp;storeid=26801008100050510d7cd29140000006e01004fb2534806b06bd1e6d3557ef&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8b9e25730f3b5600520de8a54021bdd&amp;filekey=30440201010430302e02016e040253480420633862396532353733306633623536303035323064653861353430323162646402031aedf0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268010081000773b5d7cd29140000006e02004fb2534806b06bd1e6d355819&amp;ef=2&amp;bizid=1022" aeskey="fe3bef3636374e7092450530557e1c32" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=e15ac63adf91296664a92286594c9142&amp;filekey=30440201010430302e02016e04025348042065313561633633616466393132393636363461393232383635393463393134320203014fa0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268010081000a677dd7cd29140000006e03004fb3534806b06bd1e6d35584f&amp;ef=3&amp;bizid=1022" externmd5="867dc11eae53059141468842aa26ed36" width="300" height="299" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754008050, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>72</membercount>\n\t<signature>N0_V1_mP6Rzoyh|v1_+KRLHxEa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 860104894446065288, 'MsgSeq': 871416114}
2025-08-01 08:27:26 | INFO | 收到表情消息: 消息ID:201284265 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:8a0c6d3e5f8088e644f274ba364fb54d 大小:1764845
2025-08-01 08:27:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 860104894446065288
