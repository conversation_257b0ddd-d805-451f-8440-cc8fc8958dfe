2025-08-01 11:57:15 | SUCCESS | 读取主设置成功
2025-08-01 11:57:15 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-01 11:57:15 | INFO | 2025/08/01 11:57:15 GetRedisAddr: 127.0.0.1:6379
2025-08-01 11:57:15 | INFO | 2025/08/01 11:57:15 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-01 11:57:15 | INFO | 2025/08/01 11:57:15 Server start at :9000
2025-08-01 11:57:15 | SUCCESS | WechatAPI服务已启动
2025-08-01 11:57:16 | SUCCESS | 获取到登录uuid: QqcjHEWFE73OC3o_JtvV
2025-08-01 11:57:16 | SUCCESS | 获取到登录二维码: https://api.pwmqr.com/qrcode/create/?url=http://weixin.qq.com/x/QqcjHEWFE73OC3o_JtvV
2025-08-01 11:57:16 | INFO | 等待登录中，过期倒计时：240
2025-08-01 11:57:23 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-01 11:57:23 | INFO | 登录设备信息: device_name: Joseph Jackson's Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-01 11:57:23 | SUCCESS | 登录成功
2025-08-01 11:57:23 | SUCCESS | 已开启自动心跳
2025-08-01 11:57:23 | INFO | 成功加载表情映射文件，共 547 条记录
2025-08-01 11:57:23 | SUCCESS | 数据库初始化成功
2025-08-01 11:57:23 | SUCCESS | 定时任务已启动
2025-08-01 11:57:23 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-01 11:57:23 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 11:57:23 | INFO | 播客API初始化成功
2025-08-01 11:57:23 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 11:57:23 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-01 11:57:23 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-01 11:57:23 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-01 11:57:23 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-01 11:57:23 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-01 11:57:23 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-01 11:57:23 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-01 11:57:23 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-01 11:57:24 | INFO | [ChatSummary] 数据库初始化成功
2025-08-01 11:57:24 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-01 11:57:24 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-01 11:57:24 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-01 11:57:24 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-01 11:57:24 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-01 11:57:24 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-01 11:57:24 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-01 11:57:24 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-01 11:57:24 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-01 11:57:24 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-01 11:57:24 | DEBUG |   - 启用状态: True
2025-08-01 11:57:24 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-01 11:57:24 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-01 11:57:24 | DEBUG |   - Web ID: 7532989324985157172
2025-08-01 11:57:24 | DEBUG |   - Cookies配置: 已配置
2025-08-01 11:57:24 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-01 11:57:24 | DEBUG |   - 自然化响应: True
2025-08-01 11:57:24 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-01 11:57:24 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-01 11:57:24 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-01 11:57:24 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-01 11:57:24 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-01 11:57:24 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-01 11:57:24 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-01 11:57:24 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 11:57:24 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-01 11:57:24 | INFO | [RenameReminder] 开始启用插件...
2025-08-01 11:57:24 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-01 11:57:24 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-01 11:57:24 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-01 11:57:24 | INFO | 已设置检查间隔为 3600 秒
2025-08-01 11:57:24 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-01 11:57:25 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-01 11:57:25 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-01 11:57:25 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-01 11:57:25 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-01 11:57:26 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-01 11:57:26 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-01 11:57:26 | INFO | [yuanbao] 插件初始化完成
2025-08-01 11:57:26 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-01 11:57:26 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-01 11:57:26 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-01 11:57:26 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-01 11:57:26 | INFO | 处理堆积消息中
2025-08-01 11:57:28 | DEBUG | 接受到 21 条消息
2025-08-01 11:57:30 | SUCCESS | 处理堆积消息完毕
2025-08-01 11:57:30 | SUCCESS | 开始处理消息
